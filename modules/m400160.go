package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
	"slices"
)

var _ = Factory.reg(basic.NewGeneral[*g400160])

type g400160 struct {
	Config                          c400160
	RandByWeight                    *utils.RandomWeightPicker[int16, int] // 所有图标权重
	RandByWeightNoWild              *utils.RandomWeightPicker[int16, int] // 不含 Wild
	RandByFishWeight                *utils.RandomWeightPicker[int16, int] // 鱼权重
	RandByWeightNoScatter           *utils.RandomWeightPicker[int16, int] // 不含 Scatter
	RandByWeightNoScatterNoFishIcon *utils.RandomWeightPicker[int16, int] // 不含 Scatter 和鱼币
	RandByWeightNoScatterNoFish     *utils.RandomWeightPicker[int16, int] // 不含 Scatter 和鱼
}

type c400160 struct {
	MaxPayout      int64
	Row            int
	Column         int
	WildIcon       int16
	FishcoinIcon   int16
	PayoutTable    [][]int16
	IconWeight     map[int16]int
	FishWeight     map[int16]int
	FishcoinWeight map[int16]int16
	Pattern        [][]basic.Position // 连线模式
	FreeSpin       FreeSpin4000002    // 免费旋转配置
	FreeLv         map[int][]int      // 免费等级配置
	MinLimit       map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

type FreeSpin4000002 struct {
	Icon       int16
	Number     int16
	FirstCount int16
	MoreLv     int16
	Lv         int16
}

type FreeSpinLv struct {
	TotalId       int
	TotalLv       int
	WildIconCount int
	TotalSpin     int
	GameFactor    int
}

func (g *g400160) Init(config []byte) {
	g.Config = utils.ParseYAML[c400160](config)
	g.RandByWeight = utils.NewRandomWeightPicker(g.Config.IconWeight)
	// 生成不含 Scatter 的权重
	noScatterWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.FreeSpin.Icon {
			noScatterWeight[icon] = weight
		}
	}
	// 生成不含 Scatter 和鱼币的权重
	noScatterNoFishcoinIconWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.FishcoinIcon && icon != g.Config.FreeSpin.Icon {
			noScatterNoFishcoinIconWeight[icon] = weight
		}
	}
	// 生成不含 Scatter 和鱼的权重
	noScatterNoFishWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != 7 && icon != g.Config.FreeSpin.Icon {
			noScatterNoFishWeight[icon] = weight
		}
	}
	g.RandByFishWeight = utils.NewRandomWeightPicker(g.Config.FishWeight)
	g.RandByWeightNoScatter = utils.NewRandomWeightPicker(noScatterWeight)
	g.RandByWeightNoScatterNoFishIcon = utils.NewRandomWeightPicker(noScatterNoFishcoinIconWeight)
	g.RandByWeightNoScatterNoFish = utils.NewRandomWeightPicker(noScatterNoFishWeight)
}

func (g400160) ID() int32 {
	return 400160
}

func (m g400160) Line() int32 { // 线数
	return 20
}

func (m g400160) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g400160) Exception(code int32) string {
	return "{}"
}

func (g *g400160) Spin(rd *rand.Rand) basic.ISpin {
	spin := games.S400160{
		IsFree:     false,
		IsHot:      false,
		IsFishCoin: false,
		IsFish:     false,
	}
	page := games.P400160{}
	s := games.S400160{}
	if rd.Intn(100) < 1 {
		spin.IsFish = true
	} else if rd.Intn(100) < 4 {
		spin.IsFishCoin = true
	}
	if spin.IsFish {
		page = g.generateFishRainPage(rd)
	} else if spin.IsFishCoin {
		page = g.generateFishCoinPage(rd)
	} else {
		page, s = g.generatePage(rd)
		spin.IsFree = s.IsFree
		spin.IsHot = s.IsHot
	}
	allPay := int32(0)
	linesInfo := g.getPayout(page.Grid)

	for _, line := range linesInfo {
		allPay += int32(line.Iwin.Win)
	}
	page.Lines = linesInfo
	page.Pays = page.Dop.FishcoinWin + page.Dop.FishWin + allPay
	spin.Pays = page.Pays
	if spin.IsHot || spin.IsFree {
		TotalLV := 0
		//free模式计算初始等级
		scatterCount := 0
		for _, i := range page.Grid {
			for _, j := range i {
				if j == 0 {
					scatterCount++
				}
			}
			TotalLV = scatterCount - 3
		}
		//构造初始免费数据
		totalFreeSpins := []FreeSpinLv{}
		for i := 1; i <= TotalLV; i++ {
			totalFreeSpin := FreeSpinLv{
				TotalId:       g.Config.FreeLv[i][0],
				TotalSpin:     g.Config.FreeLv[i][2],
				TotalLv:       i,
				GameFactor:    g.Config.FreeLv[i][3],
				WildIconCount: g.Config.FreeLv[i][1],
			}
			totalFreeSpins = append(totalFreeSpins, totalFreeSpin)
		}

		// 第一次进 free 或者 hot模式的总旋转数
		page.FreeInfo.Total = 10
		page.FreeInfo.Award = 10
		page.FreeInfo.Remain = 10
		page.FreeInfo.Nesting = 1
		page.Dop.Multiplier = 1

		for i := 0; i < len(totalFreeSpins); i++ {
			stackFreeGame := belatra.StackFreeGameOne{ID: totalFreeSpins[i].TotalId, Awarded: false, N: totalFreeSpins[i].TotalSpin, GameFactor: totalFreeSpins[i].GameFactor}
			page.Dop.StackFreeGame = append(page.Dop.StackFreeGame, stackFreeGame)
		}
		page.FreeInfo.WildDir = utils.IfElse(spin.IsHot, 5, 0)
		spin.Pages = append(spin.Pages, page)
		totalSpin := 0
		totalPays := int32(0)
		totalwildIcon := g.Config.FreeLv[TotalLV][1]
		count := 0
		for i := 0; i <= TotalLV; i++ {
			localSpin := g.Config.FreeLv[i][2]
			totalSpin += g.Config.FreeLv[i][2]
			//freePage := games.P400160{}
			//初始旋转为10次
			for j := 0; j < localSpin; j++ {
				allLineWin := int32(0)
				allFishWin := int32(0)
				count++
				grid := g.generateFreePage(rd, spin.Pages[len(spin.Pages)-1].Dop)
				wildIcon := g.countWildIcon(grid.Grid)
				freePage := games.P400160{
					Grid:  grid.Grid,
					Lines: g.getPayout(grid.Grid),
					FreeInfo: belatra.FreeInfo400160{
						Total:   totalSpin,
						Remain:  utils.IfElse(j == 0 && i == 0, localSpin-j-1, localSpin-j),
						Award:   0,
						Nesting: i + 1,
						WildDir: 0,
					},
					SubGameInfos: grid.SubGameInfos,
					Dop:          grid.Dop,
				}
				//	lineWin := int64(0)
				for _, line := range freePage.Lines {
					//	lineWin += line.Iwin.Win
					allLineWin += int32(line.Iwin.Win)
				}
				allFishWin = freePage.Dop.FishWin + freePage.Dop.FishcoinWin
				//鱼的收益需要乘当前等级的倍率
				allFishWin *= int32(g.Config.FreeLv[i][3])
				allWin := allLineWin + allFishWin
				freePage.Pays = allWin
				freePage.FreeInfo.AllWin = int(allWin)
				totalPays += freePage.Pays

				//完成 wildIcon 统计
				totalwildIcon += wildIcon

				//处理升级
				//等级到8时处理停止升级
				if TotalLV < 8 && totalwildIcon >= g.Config.FreeLv[TotalLV+1][1] {
					TotalLV += 1
					stackFreeGame := belatra.StackFreeGameOne{ID: g.Config.FreeLv[TotalLV][0], Awarded: false, N: g.Config.FreeLv[TotalLV][2], GameFactor: g.Config.FreeLv[TotalLV][3]}
					freePage.Dop.StackFreeGame = append(freePage.Dop.StackFreeGame, stackFreeGame)
				}
				if i > 0 {
					freePage.Dop.StackFreeGame[i-1].Awarded = true
					freePage.Dop.CurFreeGameDescription = freePage.Dop.StackFreeGame[i-1]
				}
				spin.Pages = append(spin.Pages, freePage)
				spin.Pays = totalPays
			}

		}
		if count == totalSpin {
			lastDop := spin.Pages[len(spin.Pages)-1].Dop
			lastDop.FishcoinBox, lastDop.FishcoinOld = g.rollBackFishicon(spin.Pages[len(spin.Pages)-1].Grid)
			spin.Pages = append(spin.Pages, games.P400160{
				Grid:  spin.Pages[len(spin.Pages)-1].Grid,
				Lines: g.getPayout(spin.Pages[len(spin.Pages)-1].Grid),
				FreeInfo: belatra.FreeInfo400160{
					Total:   totalSpin,
					Remain:  0,
					Award:   0,
					Nesting: TotalLV + 1,
					WildDir: 0,
					AllWin:  int(spin.Pays),
				},
				SubGameInfos: spin.Pages[len(spin.Pages)-1].SubGameInfos,
				Dop:          lastDop,
			})
			spin.Pages[len(spin.Pages)-1].Dop.StackFreeGame = slices.Clip(spin.Pages[len(spin.Pages)-1].Dop.StackFreeGame[:0])
		}
		//普通模式 spin 一个 page
	} else {
		spin.Pages = append(spin.Pages, page)
	}

	return &spin
}

func (g *g400160) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (g *g400160) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400160)
	s.GameId = g.ID()
	s.Line = g.Line()
	// 对所有页面进行随机再生处理
	//for i := range s.Pages {
	//	s.Pages[i].Grid = g.regenerateGrid(s.Pages[i].Grid, s.Pages[i].Lines, salt)
	//}

	// 最大赔付
	if s.Pays > int32(g.Config.MaxPayout) {
		s.Pays = int32(g.Config.MaxPayout)
	}
	return s
}

func (g *g400160) regenerateGrid(originalGrid [][]int16, lines []belatra.LinesInfo, salt *rand.Rand) [][]int16 {
	if len(lines) == 0 {
		// 没有中奖线，直接返回原棋盘
		return originalGrid
	}
	// 创建新的棋盘副本
	newGrid := make([][]int16, len(originalGrid))
	copy(newGrid, originalGrid)

	// 获取所有中奖位置
	winningPositions := g.getWinningPositions(originalGrid, lines)

	// 获取非中奖位置的图标
	nonWinningIcons := g.getNonWinningIcons(originalGrid, winningPositions)

	// 如果没有非中奖图标，直接返回原棋盘
	if len(nonWinningIcons) == 0 {
		return originalGrid
	}

	// 使用 Fisher-Yates 算法打乱非中奖图标
	utils.ShuffleIcons(nonWinningIcons, salt)

	// 将打乱后的图标重新放置到非中奖位置
	iconIndex := 0
	for i := 0; i < g.Config.Row; i++ {
		for j := 0; j < g.Config.Column; j++ {
			if !winningPositions[i][j] {
				if iconIndex < len(nonWinningIcons) {
					newGrid[i][j] = nonWinningIcons[iconIndex]
					iconIndex++
				}
			}
		}
	}
	// 验证重排后不会产生新的中奖组合
	if g.hasNewWinningLines(newGrid, originalGrid, lines) {
		// 如果产生了新的中奖线，返回原棋盘
		return originalGrid
	}

	return newGrid
}

func (g *g400160) getWinningPositions(grid [][]int16, lines []belatra.LinesInfo) [][]bool {
	winningPositions := g.makeBoolGrid()
	for _, line := range lines {
		if line.ID < len(g.Config.Pattern) {
			pattern := g.Config.Pattern[line.ID]
			symbols := make([]int16, len(pattern))
			for i, pos := range pattern {
				row := pos.Row()
				col := pos.Column()
				if row < 0 || row >= 4 || col < 0 || col >= 5 {
					continue
				}
				symbols[i] = grid[row][col]
			}
			if ok, _, _ := g.checkLine(symbols); ok {
				for i, pos := range pattern {
					row := pos.Row()
					col := pos.Column()
					if row >= 0 && row < int32(g.Config.Row) && col >= 0 && col < int32(g.Config.Column) {
						if i < len(line.Iwin.OnLine) && line.Iwin.OnLine[i] != 127 {
							winningPositions[row][col] = true
						}
					}
				}
			}
		}
	}
	return winningPositions
}

func (g *g400160) getNonWinningIcons(grid [][]int16, winningPositions [][]bool) []int16 {
	var nonWinningIcons []int16
	for i := 0; i < g.Config.Row; i++ {
		for j := 0; j < g.Config.Column; j++ {
			if !winningPositions[i][j] {
				nonWinningIcons = append(nonWinningIcons, grid[i][j])
			}
		}
	}
	return nonWinningIcons
}

func (g *g400160) hasNewWinningLines(newGrid, originalGrid [][]int16, originalLines []belatra.LinesInfo) bool {

	// 创建一个临时的 S400163 结构体用于计算赔付
	newLines := g.getPayout(newGrid)

	// 如果新的中奖线数量不同，说明产生了新的中奖组合
	if len(newLines) != len(originalLines) {
		return true
	}

	// 检查每条中奖线的赔付是否相同
	originalPayouts := make(map[int]int64)
	for _, line := range originalLines {
		originalPayouts[line.ID] = line.Iwin.Win
	}

	for _, line := range newLines {
		if originalPayout, exists := originalPayouts[line.ID]; !exists || originalPayout != line.Iwin.Win {
			return true
		}
	}

	return false
}

func (g g400160) Rule() string {
	gs := map[string]any{
		"gamegroup":             "base",
		"doubleAssortment":      []string{"off"},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         []int{1, 2, 3, 4, 5, 6, 7, 10, 15, 20, 25, 30, 40, 50, 75, 100, 125, 250},
		"denomAssortment_cents": []int{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]interface{}{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus": map[string]interface{}{
			"wasBuy":   0,
			"selectId": -1,
			"buyTotalBetK": []map[string]interface{}{
				{"id": 0, "cost": 100, "prefix2": "_BASE_FG", "rtp": 96.82},
				{"id": 1, "cost": 20, "prefix2": "_BASE_DYNAMITE", "rtp": 96.78},
				{"id": 2, "cost": 50, "prefix2": "_BASE_COINFISH", "rtp": 96.8},
				{"id": 3, "cost": 400, "prefix2": "_BASE_FG_HOT", "rtp": 96.84},
			},
		},
		"outRatesVolatility":    nil,
		"placedbet":             20,
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            20,
		"betPerLine":            1,
		"nlines":                20,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 5000,
		"analInfo": map[string]interface{}{
			"formula": map[string]interface{}{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]interface{}{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles": [][]int{
				{2, 2, 2, 2, 2},
				{1, 1, 1, 1, 1},
				{3, 3, 3, 3, 3},
				{0, 0, 0, 0, 0},
				{0, 1, 2, 1, 0},
				{3, 2, 1, 2, 3},
				{1, 2, 3, 2, 1},
				{2, 1, 0, 1, 2},
				{1, 1, 0, 1, 1},
				{2, 2, 3, 2, 2},
				{0, 0, 1, 0, 0},
				{3, 3, 2, 3, 3},
				{0, 1, 0, 1, 0},
				{3, 2, 3, 2, 3},
				{1, 0, 1, 0, 1},
				{2, 3, 2, 3, 2},
				{0, 1, 1, 1, 0},
				{3, 2, 2, 2, 3},
				{2, 3, 3, 3, 2},
				{1, 0, 0, 0, 1},
				{2, 1, 2, 1, 2},
				{1, 2, 1, 2, 1},
				{1, 2, 2, 2, 1},
				{1, 1, 2, 1, 1},
				{2, 2, 1, 2, 2},
			},
			"symbolNames": []string{
				"scatter", "fisherman", "octopus", "crab", "fishing_rod",
				"bucket", "hook", "fishes", "a", "k", "q", "j", "coin_fish",
			},
			"baseReels": [][]int{
				{7, 7, 7, 7, 9, 9, 9, 1, 8, 8, 8, 0, 10, 10, 10, 5, 5, 5, 5, 5, 5, 2, 2, 2, 10, 10, 10, 10, 10, 8, 8, 8, 8, 9, 9, 9, 11, 11, 7, 7, 7, 2, 2, 7, 7, 7, 7, 12, 9, 9, 8, 8, 3, 3, 3, 3, 3, 3, 9, 9, 9, 9, 0, 11, 11, 11, 8, 8, 4, 4, 12, 10, 10, 8, 8, 8, 9, 9, 9, 9, 9, 11, 11, 11, 10, 10, 10, 5, 5, 6, 6, 6, 6, 6, 6, 2, 2, 2, 2, 2, 2, 3, 3, 3, 5, 5, 7, 7, 7, 10, 10, 10, 8, 8, 8, 8, 11, 11, 11, 11, 5, 5, 3, 3, 3, 3, 6, 6, 6, 6, 6, 6, 4, 4, 4, 4, 4, 4, 11, 11, 11, 11, 12, 9, 9, 9, 1, 10, 10, 10, 10, 2, 2, 2, 2, 2, 2, 8, 8},
				{5, 5, 5, 5, 12, 8, 8, 3, 3, 3, 3, 10, 10, 4, 4, 4, 4, 4, 4, 10, 10, 10, 0, 9, 9, 9, 9, 9, 11, 11, 11, 11, 12, 5, 5, 5, 5, 8, 8, 8, 9, 9, 9, 10, 10, 10, 10, 10, 7, 7, 7, 7, 9, 9, 11, 11, 11, 1, 10, 10, 10, 7, 7, 7, 7, 12, 6, 6, 6, 6, 6, 6, 11, 11, 11, 8, 8, 8, 8, 0, 9, 9, 9, 9, 12, 4, 4, 4, 4, 4, 4, 4, 4, 10, 10, 10, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8, 8, 11, 11, 4, 4, 4, 4, 7, 7, 7, 7, 8, 8, 8, 8, 0, 10, 10, 10, 7, 7, 7, 7, 8, 8, 8, 3, 3, 3, 11, 11, 11, 11, 10, 10, 10, 10, 1, 11, 11, 11, 6, 6, 6, 6, 12, 2, 2, 2, 2, 9, 9, 11, 11, 11, 0, 10, 10, 10},
				{7, 7, 7, 2, 2, 2, 2, 5, 5, 5, 5, 8, 8, 8, 0, 11, 11, 11, 11, 10, 10, 10, 10, 4, 4, 4, 4, 1, 2, 2, 2, 2, 2, 2, 6, 6, 6, 6, 10, 10, 10, 10, 10, 11, 11, 11, 8, 8, 8, 12, 7, 7, 7, 9, 9, 9, 1, 11, 11, 11, 5, 5, 5, 5, 2, 2, 2, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5, 8, 8, 8, 0, 10, 10, 10, 10, 6, 6, 6, 6, 4, 4, 4, 4, 9, 9, 9, 8, 8, 8, 8, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4, 7, 7, 8, 8, 8, 11, 11, 11, 10, 10, 10, 10, 12, 6, 6, 6, 6, 11, 11, 11, 11, 10, 10, 10, 8, 8, 8, 7, 7, 7, 7, 11, 11, 11, 0, 9, 9, 9, 12, 5, 5, 5, 5, 7, 7, 8, 8, 9, 9, 9, 10, 10, 10, 9, 9, 9, 1, 10, 10, 10, 10, 3, 3, 3, 3, 3},
				{10, 10, 10, 0, 11, 11, 11, 3, 3, 3, 4, 4, 9, 9, 10, 10, 10, 8, 8, 8, 8, 5, 5, 5, 5, 2, 2, 2, 2, 2, 2, 7, 7, 7, 7, 12, 11, 11, 11, 11, 1, 8, 8, 8, 12, 10, 10, 10, 8, 8, 8, 6, 6, 6, 6, 7, 7, 9, 9, 9, 9, 10, 10, 10, 10, 10, 0, 9, 9, 9, 9, 5, 5, 5, 3, 3, 3, 7, 7, 7, 9, 9, 9, 8, 8, 8, 8, 8, 3, 3, 3, 3, 3, 4, 4, 4, 10, 10, 10, 10, 8, 8, 8, 11, 11, 11, 11, 9, 9, 9, 7, 7, 7, 7, 7, 10, 10, 11, 11, 11, 10, 10, 10, 1, 8, 8, 8, 3, 3, 3, 3, 3, 3, 3, 7, 7, 10, 10, 10, 0, 11, 11, 11, 7, 7, 7, 7, 2, 2, 2, 2, 8, 8, 8, 9, 9, 9, 11, 11, 8, 8, 8, 8, 8, 8, 10, 10, 10, 3, 3, 3, 3, 8, 8, 9, 9, 9, 1, 10, 10, 10, 11, 11, 12, 5, 5, 5, 5, 2, 2, 2, 7, 7, 7, 7, 11, 11, 11, 0, 9, 9, 9, 8, 8, 8, 11, 11, 11, 4, 4, 4, 4, 4, 4},
				{9, 9, 9, 9, 0, 10, 10, 10, 11, 11, 11, 11, 2, 2, 6, 6, 12, 9, 9, 9, 10, 10, 10, 10, 10, 1, 11, 11, 11, 7, 7, 7, 12, 11, 11, 11, 5, 5, 5, 8, 8, 8, 11, 11, 11, 2, 2, 2, 2, 10, 10, 10, 10, 0, 8, 8, 8, 7, 7, 7, 7, 7, 7, 3, 3, 3, 3, 10, 10, 10, 10, 9, 9, 9, 6, 6, 6, 6, 8, 8, 8, 9, 9, 9, 3, 3, 3, 8, 8, 8, 11, 11, 11, 6, 6, 6, 10, 10, 10, 10, 9, 9, 9, 9, 8, 8, 8, 8, 8, 12, 3, 3, 3, 12, 11, 11, 11, 1, 8, 8, 8, 8, 4, 4, 2, 2, 2, 12, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 3, 3, 8, 8, 8, 11, 11, 11, 11, 9, 9, 9, 8, 8, 8, 0, 10, 10, 10, 2, 2, 2, 12, 9, 9, 6, 6, 6, 8, 8, 8, 11, 11, 11, 5, 5, 3, 3, 3, 3, 4, 4, 7, 7, 7, 10, 10, 10, 11, 11, 11, 11, 0, 8, 8, 8, 8, 7, 7, 7, 7},
			},
			"freeReels": [][]int{
				{6, 6, 7, 7, 7, 7, 11, 11, 11, 5, 5, 5, 12, 10, 10, 10, 1, 8, 8, 8, 7, 7, 7, 10, 10, 3, 3, 3, 9, 9, 9, 1, 10, 10, 10, 7, 7, 12, 11, 11, 11, 11, 9, 9, 9, 7, 7, 7, 10, 10, 12, 8, 8, 8, 5, 5, 5, 10, 10, 7, 7, 7, 2, 2, 3, 3, 3, 3, 11, 11, 4, 4, 4, 4, 4, 4, 10, 10, 10, 12, 5, 5, 5, 9, 9, 9, 1, 8, 8, 8, 7, 7, 6, 6, 12, 11, 11, 11, 1, 9, 9, 9, 9, 2, 2, 10, 10, 5, 5, 5, 7, 7, 7, 7, 7, 6, 6, 6, 10, 10, 10, 10, 6, 6, 6, 4, 4, 4, 6, 6, 6, 11, 11, 11, 11, 1, 10, 10, 10, 12, 6, 6, 6, 9, 9, 5, 5, 5, 5, 10, 10, 10, 11, 11, 11, 11, 4, 4, 7, 7, 7, 9, 9, 9, 9, 11, 11, 11, 8, 8, 8, 3, 3, 3, 10, 10, 10, 10, 11, 11, 2, 2, 2, 10, 10, 10, 4, 4, 4, 5, 5, 5, 5, 5, 5, 8, 8, 6, 6, 6, 6},
				{4, 4, 4, 5, 5, 5, 10, 10, 10, 10, 7, 7, 12, 5, 5, 5, 11, 11, 11, 11, 3, 3, 7, 7, 2, 2, 2, 10, 10, 5, 5, 6, 6, 6, 8, 8, 8, 1, 10, 10, 10, 9, 9, 6, 6, 6, 6, 11, 11, 11, 11, 1, 9, 9, 9, 9, 9, 4, 4, 4, 4, 7, 7, 7, 9, 9, 9, 1, 10, 10, 10, 5, 5, 5, 6, 6, 8, 8, 2, 2, 2, 2, 2, 2, 5, 5, 5, 3, 3, 4, 4, 4, 4, 7, 7, 7, 7, 9, 9, 11, 11, 6, 6, 6, 4, 4, 4, 3, 3, 3, 12, 7, 7, 7, 10, 10, 10, 10, 1, 8, 8, 8, 12, 11, 11, 11, 11, 11, 10, 10, 10, 9, 9, 9, 9, 4, 4, 7, 7, 5, 5, 5, 5, 5, 5, 8, 8, 8, 8, 8, 6, 6, 2, 2, 2, 2, 2, 10, 10, 10, 6, 6, 6, 9, 9, 9, 8, 8, 8, 8, 10, 10, 5, 5, 5, 9, 9, 9, 1, 11, 11, 11, 5, 5, 6, 6, 6, 6, 10, 10, 10, 1, 9, 9, 9, 9},
				{7, 7, 2, 2, 12, 10, 10, 10, 10, 5, 5, 5, 5, 4, 4, 8, 8, 6, 6, 9, 9, 6, 6, 10, 10, 10, 12, 5, 5, 10, 10, 10, 10, 1, 11, 11, 11, 4, 4, 5, 5, 3, 3, 8, 8, 8, 2, 2, 2, 5, 5, 5, 6, 6, 6, 10, 10, 10, 1, 11, 11, 11, 9, 9, 12, 10, 10, 10, 8, 8, 8, 8, 2, 2, 10, 10, 7, 7, 11, 11, 11, 11, 1, 8, 8, 8, 10, 10, 10, 10, 5, 5, 5, 12, 7, 7, 7, 7, 10, 10, 10, 6, 6, 6, 6, 6, 8, 8, 8, 7, 7, 5, 5, 5, 5, 12, 10, 10, 4, 4, 7, 7, 7, 7, 8, 8, 8, 10, 10, 10, 11, 11, 12, 9, 9, 9, 5, 5, 5, 6, 6, 6, 6, 6, 10, 10, 11, 11, 11, 9, 9, 9, 1, 10, 10, 10, 2, 2, 2, 3, 3, 7, 7, 7, 7, 10, 10, 10, 10, 8, 8, 8, 5, 5, 4, 4, 4, 9, 9, 9, 9},
				{9, 9, 9, 5, 5, 12, 11, 11, 11, 1, 10, 10, 10, 6, 6, 6, 3, 3, 10, 10, 10, 10, 8, 8, 8, 8, 5, 5, 5, 4, 4, 4, 4, 7, 7, 7, 11, 11, 11, 11, 11, 1, 9, 9, 9, 5, 5, 2, 2, 2, 8, 8, 8, 5, 5, 5, 7, 7, 7, 12, 11, 11, 9, 9, 9, 3, 3, 3, 4, 4, 4, 10, 10, 10, 1, 11, 11, 11, 7, 7, 7, 2, 2, 2, 2, 6, 6, 12, 8, 8, 5, 5, 10, 10, 7, 7, 4, 4, 4, 9, 9, 6, 6, 6, 6, 11, 11, 11, 1, 10, 10, 10, 5, 5, 5, 6, 6, 6, 3, 3, 3, 3, 3, 7, 7, 7, 7, 7, 7, 12, 10, 10, 10, 10, 4, 4, 4, 8, 8, 8, 8, 9, 9, 9, 9, 6, 6, 6, 10, 10, 5, 5, 5, 5, 8, 8, 8, 1, 9, 9, 9, 11, 11, 11, 11, 10, 10, 6, 6, 6, 6, 7, 7, 7, 10, 10, 10, 9, 9, 9},
				{3, 3, 3, 6, 6, 6, 6, 6, 4, 4, 4, 8, 8, 10, 10, 10, 10, 10, 12, 9, 9, 9, 9, 5, 5, 5, 8, 8, 8, 10, 10, 2, 2, 9, 9, 9, 1, 11, 11, 11, 3, 3, 8, 8, 8, 12, 10, 10, 7, 7, 7, 11, 11, 3, 3, 3, 7, 7, 7, 10, 10, 10, 1, 8, 8, 8, 8, 6, 6, 9, 9, 9, 9, 5, 5, 3, 3, 3, 10, 10, 10, 4, 4, 4, 7, 7, 12, 10, 10, 7, 7, 11, 11, 11, 11, 1, 10, 10, 10, 10, 12, 9, 9, 9, 8, 8, 8, 8, 7, 7, 7, 11, 11, 11, 11, 1, 10, 10, 10, 5, 5, 5, 5, 9, 9, 9, 8, 8, 8, 8, 6, 6, 6, 6, 6, 6, 11, 11, 11, 11, 9, 9, 9, 10, 10, 2, 2, 5, 5, 5, 10, 10, 10, 4, 4, 4, 4, 3, 3, 3, 9, 9, 8, 8, 8, 11, 11, 11, 1, 10, 10, 10, 6, 6, 6, 6, 7, 7, 7, 7},
			},
			"statTablo": map[string]interface{}{
				"volatility": 7,
				"bigwin":     7,
				"epicwin":    8,
				"bonus":      8,
				"show":       1,
				"rtp":        96.72,
			},
			"maxWinFreq_big":      7807656,
			"VIP_maxWinFreq_big":  5914854,
			"arrlimits_winLimitK": []int{5000},
			"coinFishXBet":        []int{10, 20, 50, 100},
			"scatterIds":          []int{0},
			"wildIds":             []int{1},
			"minScatters":         []int{3},
			"outRates_vipmode":    96.76,
			"xbetsFish": [][]int{
				{2, 2500},
				{5, 750},
				{10, 450},
				{15, 200},
				{20, 100},
				{25, 80},
				{50, 40},
				{100, 25},
				{200, 10},
				{300, 10},
				{400, 8},
				{500, 5},
				{1000, 50},
			},
			"coinfishAssortment": []int{10, 20, 50, 100},
			"freeGamePortionByFisher": map[string]interface{}{
				"4":  map[string]interface{}{"id": 0, "awarded": false, "n": 10, "gameFactor": 2},
				"8":  map[string]interface{}{"id": 1, "awarded": false, "n": 10, "gameFactor": 3},
				"12": map[string]interface{}{"id": 2, "awarded": false, "n": 10, "gameFactor": 10},
				"16": map[string]interface{}{"id": 3, "awarded": false, "n": 1, "gameFactor": 20},
				"17": map[string]interface{}{"id": 4, "awarded": false, "n": 1, "gameFactor": 30},
				"18": map[string]interface{}{"id": 5, "awarded": false, "n": 1, "gameFactor": 40},
				"19": map[string]interface{}{"id": 6, "awarded": false, "n": 1, "gameFactor": 50},
				"20": map[string]interface{}{"id": 7, "awarded": false, "n": 1, "gameFactor": 100},
			},
			"volatility":      12,
			"sasAdditionalId": "BTB",
			"sasPaytableId":   "BTB960",
		},
		"helpInfo": map[string]interface{}{
			"paytable": []interface{}{
				[]interface{}{[]int{0, 8}},
				[]interface{}{[]int{1, 1}},
				[]interface{}{[]int{2, 4}, []int{5, 2000}, []int{4, 500}, []int{3, 200}},
				[]interface{}{[]int{3, 4}, []int{5, 1000}, []int{4, 300}, []int{3, 100}},
				[]interface{}{[]int{4, 4}, []int{5, 200}, []int{4, 80}, []int{3, 40}},
				[]interface{}{[]int{5, 4}, []int{5, 100}, []int{4, 40}, []int{3, 20}},
				[]interface{}{[]int{6, 4}, []int{5, 80}, []int{4, 30}, []int{3, 15}},
				[]interface{}{[]int{7, 4}, []int{5, 60}, []int{4, 20}, []int{3, 10}},
				[]interface{}{[]int{8, 4}, []int{5, 40}, []int{4, 15}, []int{3, 8}},
				[]interface{}{[]int{9, 4}, []int{5, 35}, []int{4, 14}, []int{3, 7}},
				[]interface{}{[]int{10, 4}, []int{5, 30}, []int{4, 12}, []int{3, 6}},
				[]interface{}{[]int{11, 4}, []int{5, 25}, []int{4, 8}, []int{3, 4}},
				[]interface{}{[]int{12, 32}},
				[]interface{}{[]int{13, 16}},
				[]interface{}{[]int{14, 32}},
			},
			"fg": map[string]interface{}{
				"firstAward":   10,
				"nestingAward": 10,
				"limit":        50,
				"portions":     10,
			},
			"doubles": []interface{}{[]interface{}{"off", 0, 0}},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"antiDynamiteBet":        nil,
		"dramshow":               nil,
		"versions": map[string]interface{}{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]interface{}{
			"maxWinLimitK":         5000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 5000,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []int{20},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox": [][]int{
			{4, 4, 5, 4, 4},
			{9, 4, 5, 9, 4},
			{9, 4, 5, 9, 4},
			{9, 4, 4, 9, 4},
		},
		"stopBox": [][]int{
			{4, 4, 5, 4, 4},
			{9, 4, 5, 9, 4},
			{9, 4, 5, 9, 4},
			{9, 4, 4, 9, 4},
		},
		"incutId": 4,
		"vipMode": map[string]interface{}{
			"on":             0,
			"vipBetK":        1.25,
			"wasBuyVip":      0,
			"vip_noSpecSeed": false,
		},
		"setVip_inFreeSpinAlways": -1,
		"helpseed":                true,
		"dop": map[string]interface{}{
			"multiplier":             1,
			"stackFreeGame":          []interface{}{},
			"curFreeGameDescription": map[string]interface{}{},
			"fishBox": [][]int{
				{4, 4, 5, 4, 4},
				{9, 4, 5, 9, 4},
				{9, 4, 5, 9, 4},
				{9, 4, 4, 9, 4},
			},
			"fishWin": 0,
			"fishcoinBox": [][]int{
				{4, 4, 5, 4, 4},
				{9, 4, 5, 9, 4},
				{9, 4, 5, 9, 4},
				{9, 4, 4, 9, 4},
			},
			"fishcoinOldView": []interface{}{},
			"fishcoinOld":     []interface{}{},
			"fishcoinWin":     0,
			"nTotFisher":      0,
			"fishDynamiteBox": []map[string]int{
				{"r": 2, "c": 1},
				{"r": 3, "c": 0},
				{"r": 2, "c": 2},
				{"r": 0, "c": 4},
				{"r": 1, "c": 3},
				{"r": 1, "c": 1},
				{"r": 3, "c": 2},
				{"r": 3, "c": 4},
				{"r": 2, "c": 4},
				{"r": 2, "c": 0},
				{"r": 0, "c": 3},
				{"r": 0, "c": 1},
				{"r": 1, "c": 2},
				{"r": 3, "c": 1},
				{"r": 1, "c": 4},
				{"r": 2, "c": 3},
			},
		},
	}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (g400160) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	case 2:
		return 2000
	case 3:
		return 5000
	case 4:
		return 40000
	case 5:
		return 125
	default:
		return 100
	}
}

func (g g400160) generatePage(rd *rand.Rand) (p games.P400160, s games.S400160) {
	cols, rows := g.Config.Column, g.Config.Row
	icons := g.RandByWeight.More(cols*rows, rd)
	num := 0
	for i, icon := range icons {
		if icon == g.Config.FreeSpin.Icon {
			num++
		}
		//限制hot模式下最大scatter数量为5
		if num > 5 {
			icons[i] = g.RandByWeightNoScatter.One(rd)
		}
	}
	//scatter列约束，每列只能有一个scatter
	gridRows := g.scatterConstraint(icons)
	// gridRows := make([][]int16, g.Config.Row)
	// for row := 0; row < g.Config.Row; row++ {
	// 	gridRows[row] = make([]int16, g.Config.Column)
	// 	for col := 0; col < g.Config.Column; col++ {
	// 		gridRows[row][col] = icons[row*g.Config.Column+col]
	// 	}
	// }
	//生成鱼的赔率棋盘
	fishGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishGrid[row][col] == 7 {
				fishPayout := g.RandByFishWeight.One(rd)
				fishGrid[row][col] = 0 - fishPayout
			}
		}
	}
	// 生成鱼币的赔率棋盘
	fishcoinGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishcoinGrid[row][col] == g.Config.FishcoinIcon {
				fishcoinGrid[row][col] = 14
				// 鱼币棋盘鱼币坐标值为14，最后一行为最大倍率，最大倍率根据鱼币所在的行数和总行数的差值获取
				fishcoinGrid[rows-1][col] = 0 - g.Config.FishcoinWeight[int16(rows-1)-int16(row)]
			}
		}
	}
	dop, subGameInfo := g.getFishPayout(gridRows, fishGrid, fishcoinGrid)
	count := 0
	for _, icon := range icons {
		if icon == g.Config.FreeSpin.Icon {
			count++
		}
		if count >= 3 && count < 5 {
			s.IsFree = true
		}
		if count >= 5 {
			s.IsFree = false
			s.IsHot = true
		}
	}
	p.Dop = dop
	p.Grid = gridRows
	p.SubGameInfos = subGameInfo
	return p, s
}

func (g g400160) scatterConstraint(icons []int16) (newGrid [][]int16) {
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = icons[row*g.Config.Column+col]
		}
	}

	// 统计每列的scatter数量和位置
	scatterIcon := g.Config.FreeSpin.Icon
	columnScatters := make([][]int, g.Config.Column) // 每列scatter的行位置
	columnsWithoutScatter := []int{}                 // 没有scatter的列
	columnsWithExcessScatter := []int{}              // 有多个scatter的列

	// 遍历每列，统计scatter分布
	for col := 0; col < g.Config.Column; col++ {
		scatterPositions := []int{}
		for row := 0; row < g.Config.Row; row++ {
			if gridRows[row][col] == scatterIcon {
				scatterPositions = append(scatterPositions, row)
			}
		}
		columnScatters[col] = scatterPositions

		if len(scatterPositions) == 0 {
			columnsWithoutScatter = append(columnsWithoutScatter, col)
		} else if len(scatterPositions) > 1 {
			columnsWithExcessScatter = append(columnsWithExcessScatter, col)
		}
	}

	// 处理多余的scatter：与没有scatter的列交换
	for len(columnsWithExcessScatter) > 0 && len(columnsWithoutScatter) > 0 {
		excessCol := columnsWithExcessScatter[0]
		emptyCol := columnsWithoutScatter[0]

		// 从有多余scatter的列中选择一个scatter位置（保留第一个，移除其他）
		scatterPositions := columnScatters[excessCol]
		if len(scatterPositions) > 1 {
			// 选择要移除的scatter位置（除了第一个）
			removeRow := scatterPositions[1]
			// 在空列中随机选择一个位置进行交换
			targetRow := 0 // 可以选择任意行，这里选择第一行

			// 交换图标
			gridRows[removeRow][excessCol], gridRows[targetRow][emptyCol] =
				gridRows[targetRow][emptyCol], gridRows[removeRow][excessCol]

			// 更新scatter位置记录
			columnScatters[excessCol] = columnScatters[excessCol][:1] // 只保留第一个scatter
			columnScatters[emptyCol] = []int{targetRow}               // 空列现在有一个scatter

			// 更新列状态
			if len(columnScatters[excessCol]) <= 1 {
				columnsWithExcessScatter = columnsWithExcessScatter[1:] // 移除已处理的列
			}
			columnsWithoutScatter = columnsWithoutScatter[1:] // 移除已处理的空列
		}
	}

	return gridRows
}

func (g g400160) generateFreePage(rd *rand.Rand, dop belatra.Dop) games.P400160 {
	cols, rows := g.Config.Column, g.Config.Row
	//免费模式不嵌套 scatter生成
	newStack := make([]belatra.StackFreeGameOne, len(dop.StackFreeGame))
	copy(newStack, dop.StackFreeGame)
	icons := g.RandByWeightNoScatter.More(cols*rows, rd)
	nShift := int16(0)
	//处理鱼币下落
	gridRows := g.makeGrid()
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		if len(dop.FishcoinOld) > 0 {
			for _, Rows := range dop.FishcoinOld {
				if Rows["row"] < int16(g.Config.Row)-1 {
					gridRows[Rows["row"]+1][Rows["col"]] = 12
					nShift = Rows["nShift"] + 1
				}
			}
		}
		for col := 0; col < g.Config.Column; col++ {
			if gridRows[row][col] != 12 {
				gridRows[row][col] = icons[row*g.Config.Column+col]
			}
		}
	}

	//生成鱼的赔率棋盘
	fishGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishGrid[row][col] == 7 {
				fishPayout := g.RandByFishWeight.One(rd)
				fishGrid[row][col] = 0 - fishPayout
			}
		}
	}
	// 生成鱼币的赔率棋盘
	fishcoinGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishcoinGrid[row][col] == g.Config.FishcoinIcon {
				//fishcoinGrid[row][col] = 14
				// 鱼币棋盘鱼币坐标值为14，最后一行为最大倍率，最大倍率根据鱼币所在的行数和总行数的差值获取
				fishcoinGrid[row][col] = 0 - g.Config.FishcoinWeight[nShift]

			}
		}
	}
	newDop, sub := g.getFreeFishPayout(gridRows, fishGrid, fishcoinGrid)
	newDop.StackFreeGame = newStack
	return games.P400160{Dop: newDop, SubGameInfos: sub, Grid: gridRows}
}

func (g g400160) generateFishRainPage(rd *rand.Rand) games.P400160 {
	cols, rows := g.Config.Column, g.Config.Row
	fishDynamiteBox := make([]map[string]int16, 0)
	fishRainGrids := make([]int16, rows*cols)
	for i := 0; i < rows*cols; i++ {
		if rd.Intn(100) < 50 {
			fishRainGrids[i] = 7
		} else {
			fishRainGrids[i] = 200
		}
	}
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = fishRainGrids[row*g.Config.Column+col]
			if fishRainGrids[row*g.Config.Column+col] == 200 {
				gridRows[row][col] = g.RandByWeightNoScatter.One(rd)
			}
			if fishRainGrids[row*g.Config.Column+col] == 7 {
				fishDynamiteBox = append(fishDynamiteBox, map[string]int16{"r": int16(row), "c": int16(col)})
			}
		}
	}
	//生成鱼的赔率棋盘
	fishGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishGrid[row][col] == 7 {
				fishPayout := g.RandByFishWeight.One(rd)
				fishGrid[row][col] = 0 - fishPayout
			}
		}
	}
	// 生成鱼币的赔率棋盘
	fishcoinGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishcoinGrid[row][col] == g.Config.FishcoinIcon {
				fishcoinGrid[row][col] = 14
				// 鱼币棋盘鱼币坐标值为14，最后一行为最大倍率，最大倍率根据鱼币所在的行数和总行数的差值获取
				fishcoinGrid[rows-1][col] = 0 - g.Config.FishcoinWeight[int16(rows-1)-int16(row)]
			}
		}
	}
	dop, subGameInfo := g.getFishPayout(gridRows, fishGrid, fishcoinGrid)
	dop.FishDynamiteBox = fishDynamiteBox
	return games.P400160{Dop: dop, Grid: gridRows, SubGameInfos: subGameInfo}
}

func (g g400160) generateFishCoinPage(rd *rand.Rand) games.P400160 {
	cols, rows := g.Config.Column, g.Config.Row
	fishcoinGrids := make([]int16, rows*cols)
	for i := 0; i < rows*cols; i++ {
		if rd.Intn(100) < 10 {
			fishcoinGrids[i] = g.Config.FishcoinIcon
		} else {
			fishcoinGrids[i] = 200
		}
	}
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = fishcoinGrids[row*g.Config.Column+col]
			if fishcoinGrids[row*g.Config.Column+col] == 200 {
				gridRows[row][col] = g.RandByWeightNoScatterNoFish.One(rd)
			}
		}
	}
	//生成鱼的赔率棋盘
	fishGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishGrid[row][col] == 7 {
				fishPayout := g.RandByFishWeight.One(rd)
				fishGrid[row][col] = 0 - fishPayout
			}
		}
	}
	// 生成鱼币的赔率棋盘
	fishcoinGrid := cloneSlice(gridRows)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishcoinGrid[row][col] == g.Config.FishcoinIcon {
				fishcoinGrid[row][col] = 14
				// 鱼币棋盘鱼币坐标值为14，最后一行为最大倍率，最大倍率根据鱼币所在的行数和总行数的差值获取
				fishcoinGrid[rows-1][col] = 0 - g.Config.FishcoinWeight[int16(rows-1)-int16(row)]
			}
		}
	}
	dop, subGameInfo := g.getFishPayout(gridRows, fishGrid, fishcoinGrid)
	return games.P400160{Dop: dop, Grid: gridRows, SubGameInfos: subGameInfo}
}

// 切片深拷贝，防止修改鱼盘和鱼币盘数据后，会修改原棋盘的值
func cloneSlice(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}
func (g g400160) getPayout(grid [][]int16) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range g.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 4 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = grid[row][col]
		}
		if ok, payout, online := g.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   20,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		}
	}
	return linesInfo
}

// 连线
func (g g400160) checkLine(symbols []int16) (bool, int64, []int16) {
	if len(symbols) < 3 {
		return false, 0, nil
	}

	// 初始化匹配图标和计数
	matchIcon := symbols[0]
	count := 1
	wildCount := 0

	// 如果第一个图标是 WildIcon，寻找第一个非 WildIcon 作为匹配基准
	if matchIcon == g.Config.WildIcon {
		wildCount = 1
		for i := 1; i < len(symbols); i++ {
			if symbols[i] != g.Config.WildIcon {
				matchIcon = symbols[i] // 找到第一个非 WildIcon
				break
			}
			wildCount++
		}
		// 如果全是 WildIcon，按 WildIcon 赔付
		if wildCount == len(symbols) {
			return false, 0, nil
		}
	}

	// 从后续图标开始统计连续匹配
	for i := 1; i < len(symbols); i++ {
		if symbols[i] == matchIcon || symbols[i] == g.Config.WildIcon {
			count++
		} else {
			break
		}
	}
	if count >= 3 {
		payout := g.getPayoutTable(symbols, count)
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		for i := 0; i < len(symbols); i++ {
			if online[i] == 0 {
				online[i] = 127
			}
		}
		return true, int64(payout), online
	}
	return false, 0, nil
}

// 处理PayoutTable，获取倍率
func (g g400160) getPayoutTable(symbols []int16, count int) int16 {
	for _, payout := range g.Config.PayoutTable {
		if payout[0] == symbols[0] && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

// 鱼盘和鱼币盘的payout计算处理
func (g g400160) getFishPayout(grid, fishGrid, fishcoinGrid [][]int16) (belatra.Dop, []games.SubGameInfo) {
	fishPay := int32(0)
	fishcoinPay := int32(0)
	fishcoinOld := make(map[string]int16)
	subGameInfos := []games.SubGameInfo{}
	dop := belatra.Dop{
		FishDynamiteBox: []map[string]int16{},
		FishcoinOldView: []any{},
	}
	count := int16(0)
	wildCount := int32(0)
	getFishPayout := false
	getFishcoinPayout := false
	fishcoinExist := false
	fishcoinOld = make(map[string]int16)
	for row := 0; row < len(grid); row++ {
		for col := 0; col < len(grid[row]); col++ {
			if grid[row][col] == g.Config.WildIcon {
				getFishPayout = true
				getFishcoinPayout = true
				wildCount++
			}
			if grid[row][col] == 7 { // 如果棋盘有鱼的图标，计算赔率
				fishPay -= int32(fishGrid[row][col])
				count++
			}
			if grid[row][col] == g.Config.FishcoinIcon {
				fishcoinExist = true
				fishcoinPay -= int32(fishcoinGrid[len(grid)-1][col])
				// 遍历coinweight 里面的值 和最大倍率做对比
				//for k, coinWeight := range g.Config.FishcoinWeight {
				//	if fishcoinGrid[len(grid)-1][col] == coinWeight {
				//		fishcoinPay = g.Config.FishcoinWeight[k-1]
				//	}
				//}

				fishcoinOld = map[string]int16{"v": fishcoinGrid[len(grid)-1][col], "row": int16(len(grid) - 1), "col": int16(col), "nShift": int16(len(grid)-1) - int16(row)}
				dop.FishcoinOld = append(dop.FishcoinOld, fishcoinOld)
			}

		}
	}
	if getFishPayout {
		dop.FishWin = fishPay * g.Line() * wildCount
		subGameInfo := games.SubGameInfo{
			Category:      "BonusFISH",
			Type:          "4",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        dop.FishWin,
			PaidWin:       dop.FishWin,
			Attempt:       0,
			Av:            []any{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonusFISH",
			Add:           []any{},
			OnlyToBD:      []any{},
		}
		subGameInfos = append(subGameInfos, subGameInfo)
	}
	if getFishcoinPayout && fishcoinExist {
		dop.FishcoinWin = fishcoinPay * g.Line() * wildCount
		subGameInfo := games.SubGameInfo{
			Category:      "BonusFISHCOIN",
			Type:          "5",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        dop.FishcoinWin,
			PaidWin:       dop.FishcoinWin,
			Attempt:       0,
			Av:            []any{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonusFISHCOIN",
			Add:           []any{},
			OnlyToBD:      []any{},
		}
		subGameInfos = append(subGameInfos, subGameInfo)
	}
	dop.FishBox = fishGrid
	dop.FishcoinBox = fishcoinGrid
	dop.NTotFisher = utils.IfElse(getFishPayout, count, 0)
	return dop, subGameInfos
}

// 免费和热模式处理payout
func (g g400160) getFreeFishPayout(grid, fishGrid, fishcoinGrid [][]int16) (belatra.Dop, []games.SubGameInfo) {
	fishPay := int32(0)
	fishcoinPay := int32(0)
	fishcoinOld := make(map[string]int16)
	subGameInfos := []games.SubGameInfo{}
	dop := belatra.Dop{
		FishDynamiteBox: []map[string]int16{},
	}
	count := int16(0)
	wildCount := int32(0)
	getFishPayout := false
	getFishcoinPayout := false
	fishcoinExist := false
	//fishcoinOld = make(map[string]int16)
	for row := 0; row < len(grid); row++ {
		for col := 0; col < len(grid[row]); col++ {
			if grid[row][col] == g.Config.WildIcon {
				getFishPayout = true
				getFishcoinPayout = true
				wildCount++
			}
			if grid[row][col] == 7 { // 如果棋盘有鱼的图标，计算赔率
				fishPay -= int32(fishGrid[row][col])
				count++
			}
			if grid[row][col] == g.Config.FishcoinIcon {
				fishcoinExist = true
				fishcoinPay -= int32(fishcoinGrid[row][col])
				nShift := int16(0)
				// 遍历coinweight 里面的值 和最大倍率做对比
				for k, coinWeight := range g.Config.FishcoinWeight {
					if fishcoinGrid[row][col] == coinWeight {
						nShift = k
					}
					if nShift > 0 {
						fishcoinOldView := map[string]int16{"v": g.Config.FishcoinWeight[nShift-1], "row": int16(row - 1), "col": int16(col), "nShift": nShift - 1}
						dop.FishcoinOldView = append(dop.FishcoinOldView, fishcoinOldView)
					}
				}
				//处理鱼币下落逻辑
				fishcoinOld = map[string]int16{"v": fishcoinGrid[row][col], "row": int16(row), "col": int16(col), "nShift": nShift}
				dop.FishcoinOld = append(dop.FishcoinOld, fishcoinOld)

			}

		}
	}
	if getFishPayout {
		dop.FishWin = fishPay * g.Line() * wildCount
		subGameInfo := games.SubGameInfo{
			Category:      "BonusFISH",
			Type:          "4",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        dop.FishWin,
			PaidWin:       dop.FishWin,
			Attempt:       0,
			Av:            []any{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonusFISH",
			Add:           []any{},
			OnlyToBD:      []any{},
		}
		subGameInfos = append(subGameInfos, subGameInfo)
	}
	if getFishcoinPayout && fishcoinExist {
		//dop.FishcoinOld = append(dop.FishcoinOld, fishcoinOld)
		dop.FishcoinWin = fishcoinPay * g.Line() * wildCount
		subGameInfo := games.SubGameInfo{
			Category:      "BonusFISHCOIN",
			Type:          "5",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        dop.FishcoinWin,
			PaidWin:       dop.FishcoinWin,
			Attempt:       0,
			Av:            []any{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonusFISHCOIN",
			Add:           []any{},
			OnlyToBD:      []any{},
		}
		subGameInfos = append(subGameInfos, subGameInfo)
	}
	dop.FishBox = fishGrid
	dop.FishcoinBox = fishcoinGrid
	dop.NTotFisher = utils.IfElse(getFishPayout, count, 0)
	return dop, subGameInfos
}

// todo 新增免费模式等级升级方法
func (g g400160) makeGrid() [][]int16 {
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = 200
		}
	}
	return gridRows
}

func (g g400160) makeBoolGrid() [][]bool {
	gridRows := make([][]bool, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]bool, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = false
		}
	}
	return gridRows
}

func (g g400160) countWildIcon(grid [][]int16) int {
	wildIconCount := 0
	for _, i := range grid {
		for _, j := range i {
			if j == g.Config.WildIcon {
				wildIconCount++
			}
		}
	}
	return wildIconCount
}

func (g g400160) rollBackFishicon(grid [][]int16) (iconGrid [][]int16, iconOld []map[string]int16) {
	rows, cols := len(grid), len(grid[0])
	fishcoinGrid := cloneSlice(grid)
	for row := 0; row < rows; row++ {
		for col := 0; col < cols; col++ {
			if fishcoinGrid[row][col] == g.Config.FishcoinIcon {
				fishcoinGrid[row][col] = 14
				// 鱼币棋盘鱼币坐标值为14，最后一行为最大倍率，最大倍率根据鱼币所在的行数和总行数的差值获取
				fishcoinGrid[rows-1][col] = 0 - g.Config.FishcoinWeight[int16(rows-1)-int16(row)]
				fishcoinOld := map[string]int16{"v": fishcoinGrid[len(grid)-1][col], "row": int16(len(grid) - 1), "col": int16(col), "nShift": int16(len(grid)-1) - int16(row)}
				iconOld = append(iconOld, fishcoinOld)
			}
		}
	}
	return fishcoinGrid, iconOld
}

func (m g400160) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
