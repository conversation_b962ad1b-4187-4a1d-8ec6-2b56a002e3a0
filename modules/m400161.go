package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*g400161])

type g400161 struct {
	Config             c400161
	RandByWeight       *utils.RandomWeightPicker[int16, int]
	RandByNoWildWeight *utils.RandomWeightPicker[int16, int]
	RandByMulWeight    *utils.RandomWeightPicker[int32, int]
}

type c400161 struct {
	Line          int32
	IconWeight    map[int16]int
	Row           int
	Column        int
	Pattern       [][]basic.Position
	PayoutTable   map[int16]int16
	MulWeight     map[int32]int
	WildIcon      int16
	BlankIcon     int16
	SpecialBonus1 int32
	MinLimit      map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

func (g *g400161) Init(config []byte) {
	g.Config = utils.ParseYAML[c400161](config)
	g.RandByWeight = utils.NewRandomWeightPicker(g.Config.IconWeight)
	g.RandByMulWeight = utils.NewRandomWeightPicker(g.Config.MulWeight)
	noWildWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.WildIcon {
			noWildWeight[icon] = weight
		}
	}
	g.RandByNoWildWeight = utils.NewRandomWeightPicker(noWildWeight)
}

func (g400161) ID() int32 {
	return 400161
}

func (m g400161) Line() int32 { // 线数
	return 5
}

func (m g400161) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g400161) Exception(code int32) string {
	return "{}"
}

func (g *g400161) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S400161
	if rd.Int31n(100) < g.Config.SpecialBonus1 {
		spin = g.genSpec(rd, true)
	} else {
		spin = g.genNorm(rd, false)
	}
	return &spin
}

func (g *g400161) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (g *g400161) genNorm(rd *rand.Rand, isSpec bool) games.S400161 {
	Mul := int32(10)
	spin := games.S400161{
		IsSpec: isSpec,
		IsXF3:  false,
		IsXF6:  false,
	}
	Mul = g.RandByMulWeight.One(rd)
	page := games.P400161{}
	page.NeedShowFakeBonus = false
	icons := make([]int16, g.Config.Row*g.Config.Column)
	if rd.Int31n(100) < g.Config.SpecialBonus1 {
		page.Category = "Bonus1"
		icon := g.RandByWeight.One(rd)
		for i := 0; i < g.Config.Row*g.Config.Column; i++ {
			if rd.Intn(3) == 0 { // 50% 概率选择 0 或 icon
				icons[i] = 0
			} else {
				icons[i] = icon
			}
		}
	} else {
		icons = g.RandByWeight.More(g.Config.Row*g.Config.Column, rd)
		if rd.Intn(100) < 10 {
			page.NeedShowFakeBonus = true
		}
	}
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = icons[row*g.Config.Column+col]
		}
	}
	lines := g.parsePayout(gridRows)
	allPay := int32(0)
	for _, line := range lines {
		allPay += int32(line.Iwin.Win)
	}
	page.Pays = allPay
	page.Mul = Mul
	page.Lines = lines
	page.Grid = gridRows
	spin.Pays = page.Pays
	spin.Pages = append(spin.Pages, page)
	return spin
}

func (g *g400161) genSpec(rd *rand.Rand, isSpec bool) games.S400161 {
	Mul := int32(10)
	Mul = g.RandByMulWeight.One(rd)

	spin := games.S400161{
		IsSpec: isSpec,
		IsXF3:  false,
		IsXF6:  false,
	}
	page := games.P400161{}
	blankGrid := g.makeGrid()
	incutIcon := g.RandByNoWildWeight.One(rd)
	icons := g.RandByWeight.More(g.Config.Row*g.Config.Column, rd)
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = icons[row*g.Config.Column+col]
		}
	}
	page.Grid = gridRows
	page.BonusIncutId = incutIcon
	spin.Pages = append(spin.Pages, page)
	for i := 0; i < 6; i++ {
		page.Category = "Bonus2"
		if i == 0 {
			page.SpecGrid = blankGrid
			page.BonusIncutId = incutIcon
			spin.Pages = append(spin.Pages, page)
		}
		if i != 5 {
			blankGrid = g.genSpecGrid(rd, blankGrid, incutIcon)
		}
		page.SpecGrid = blankGrid
		page.BonusIncutId = incutIcon
		lines := g.parsePayout(blankGrid)
		page.Lines = lines
		allPay := int32(0)
		for _, line := range lines {
			allPay += int32(line.Iwin.Win)
		}
		page.Pays = allPay
		page.Mul = Mul
		spin.Pages = append(spin.Pages, page)
		if g.checkGrid(blankGrid) {
			break
		}
	}
	spin.Pays = page.Pays
	return spin
}

func (g *g400161) genSpecGrid(rd *rand.Rand, grid [][]int16, incutIcon int16) [][]int16 {
	blankGrid := cloneSlice400161(grid)
	for i := 0; i < g.Config.Row; i++ {
		for j := 0; j < g.Config.Column; j++ {
			if blankGrid[i][j] == g.Config.BlankIcon {
				if rd.Intn(100) < 25 {
					blankGrid[i][j] = incutIcon
				}
				if rd.Intn(100) > 85 {
					blankGrid[i][j] = g.Config.WildIcon
				}
			}
		}
	}
	return blankGrid
}

func (g g400161) parsePayout(grid [][]int16) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range g.Config.Pattern {
		symbols := make([]int16, 3)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 3 || col < 0 || col >= 3 {
				continue
			}
			symbols[i] = grid[row][col]
		}
		if ok, payout, online := g.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   5,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		}
	}
	return linesInfo
}

func (g g400161) checkLine(symbols []int16) (bool, int64, []int16) {
	if len(symbols) < 3 {
		return false, 0, nil
	}
	//一行中有任何一个空白格便不成线
	for i := 0; i < len(symbols); i++ {
		if symbols[i] == g.Config.BlankIcon {
			return false, 0, nil
		}
	}
	// 初始化匹配图标和计数
	matchIcon := symbols[0]
	count := 1
	wildCount := 0

	// 如果第一个图标是 WildIcon，寻找第一个非 WildIcon 作为匹配基准
	if matchIcon == g.Config.WildIcon {
		wildCount = 1
		for i := 1; i < len(symbols); i++ {
			if symbols[i] != g.Config.WildIcon {
				matchIcon = symbols[i] // 找到第一个非 WildIcon
				break
			}
			wildCount++
		}
		// 如果全是 WildIcon，按 WildIcon 赔付
		if wildCount == len(symbols) {
			payout := g.getPayoutTable(g.Config.WildIcon)
			online := make([]int16, len(symbols))
			copy(online, symbols[:wildCount])
			return true, int64(payout), online
		}
	}

	// 从后续图标开始统计连续匹配
	for i := 1; i < len(symbols); i++ {
		if symbols[i] == matchIcon || symbols[i] == g.Config.WildIcon {
			count++
		} else {
			break
		}
	}
	if count == 3 {
		payout := g.getPayoutTable(matchIcon)
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		return true, int64(payout), online
	}
	return false, 0, nil
}

func (g g400161) getPayoutTable(icon int16) int16 {
	for key, payout := range g.Config.PayoutTable {
		if key == icon {
			return payout
		}
	}
	return 0
}
func (g *g400161) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400161)
	s.GameId = g.ID()
	s.Line = g.Line()
	// 对所有页面进行随机再生处理
	//for i := range s.Pages {
	//	s.Pages[i].Grid = g.regenerateGrid(s.Pages[i].Grid, s.Pages[i].Lines, salt)
	//}

	return s
}

func (g400161) Rule() string {
	gs := map[string]any{
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":  1401101,
			"arrlimits_winLimitK": []int{2500, 12500},
			"baseReels": []any{
				[]int{
					1,
					3,
					3,
					0,
					2,
					2,
					0,
					2,
					2,
					6,
					6,
					6,
					6,
					1,
					4,
					4,
					5,
					5,
					5,
					5,
				},
				[]int{
					2,
					3,
					0,
					4,
					5,
					6,
					6,
					3,
					4,
					3,
					1,
					5,
					5,
					5,
					1,
					2,
					6,
					2,
					6,
					0,
				},
				[]int{
					4,
					4,
					5,
					5,
					5,
					5,
					3,
					3,
					0,
					6,
					6,
					6,
					6,
					2,
					2,
					0,
					1,
					2,
					2,
					1,
				},
			},
			"bonusReels": []any{
				[]int{
					8,
					0,
					8,
					8,
					8,
					8,
					8,
					7,
					8,
					8,
					7,
					8,
					0,
					8,
					7,
					8,
					8,
					8,
					8,
					8,
					7,
					8,
					8,
					8,
					0,
					8,
					8,
					7,
					8,
				},
				[]int{
					8,
					8,
					7,
					8,
					8,
					8,
					8,
					7,
					0,
					8,
					7,
					8,
					8,
					8,
					7,
					8,
					8,
					8,
					8,
					8,
					7,
					8,
					8,
					8,
					0,
					8,
					7,
					8,
				},
				[]int{
					8,
					7,
					8,
					8,
					8,
					0,
					8,
					7,
					8,
					8,
					7,
					0,
					8,
					8,
					7,
					8,
					8,
					8,
					8,
					8,
					7,
					8,
					8,
					8,
					8,
					8,
					7,
					8,
				},
			},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"incutIds": []int{7},
			"lineStyles": []any{
				[]int{1, 1, 1},
				[]int{0, 0, 0},
				[]int{2, 2, 2},
				[]int{0, 1, 2},
				[]int{2, 1, 0},
			},
			"maxWinFreq_small": 1512161,
			"moreFreqBuyBonuses": []any{
				[]int{23, 73},
				[]int{20, 68},
				[]int{19, 60},
			},
			"outRates_vipmode": 96.08,
			"sasAdditionalId":  "CPA",
			"sasPaytableId":    "CPA960",
			"statTablo": map[string]any{
				"bigwin":     7,
				"bonus":      9,
				"epicwin":    8,
				"rtp":        96.06,
				"show":       1,
				"volatility": 4,
			},
			"symbolNames": []string{"wild", "boat", "coins", "luck", "scroll", "petard", "bamboo", "empty", "empty"},
			"volatility":  2,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{12500, 12500},
		"betAssortment": []int{
			2,
			4,
			8,
			12,
			16,
			20,
			40,
			60,
			80,
			100,
			160,
			200,
			300,
			400,
			600,
			800,
			1000,
			2000,
		},
		"betPerGame": 10,
		"betPerLine": 2,
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{
				map[string]any{
					"cost":    5,
					"id":      0,
					"prefix2": "_BASE_VIP_BUYBONUS_MIX_0",
					"rtp":     96.4,
				},
				map[string]any{
					"cost":    10,
					"id":      1,
					"prefix2": "_BASE_VIP_BUYBONUS_MIX_1",
					"rtp":     96.51,
				},
			},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{
				[]any{"off", 0, 0},
			},
			"paytable": []any{
				[]any{
					[]int{0, 1},
					[]int{3, 250},
				},
				[]any{
					[]int{1, 4},
					[]int{3, 100},
				},
				[]any{
					[]int{2, 4},
					[]int{3, 25},
				},
				[]any{
					[]int{3, 4},
					[]int{3, 10},
				},
				[]any{
					[]int{4, 4},
					[]int{3, 8},
				},
				[]any{
					[]int{5, 4},
					[]int{3, 5},
				},
				[]any{
					[]int{6, 4},
					[]int{3, 3},
				},
				[]any{
					[]int{7, 16},
				},
				[]any{
					[]int{8, 2},
				},
			},
		},
		"helpseed":                true,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{5},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   10000,
		"minBetPerGame_cents":     nil,
		"modeLimitsWinK":          []int{2500, 12500},
		"needShowFakeBonus":       false,
		"needShowRealBonus":       false,
		"nlines":                  5,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               100,
		"present":                 "no",
		"reelstate":               0,
		"setVip_inFreeSpinAlways": -1,
		"startBox": []any{
			[]int{1, 1, 3},
			[]int{1, 3, 3},
			[]int{1, 5, 5},
		},
		"stopBox": []any{
			[]int{1, 1, 3},
			[]int{1, 3, 3},
			[]int{1, 5, 5},
		},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.5,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         12500,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 12500,
		},
	}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (g400161) InputCoef(ctl int32) int32 {
	switch ctl {
	//free
	case 1:
		return 500
	//hot
	case 2:
		return 1000
	case 3:
		return 125
	default:
		return 100
	}
}

func (g g400161) MinPayout(ctl int32) int32 {
	mode, ok := g.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * g.Line()
}

func (g g400161) makeGrid() [][]int16 {
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = 8
		}
	}
	return gridRows
}

func cloneSlice400161(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}

func (g *g400161) checkGrid(grid [][]int16) bool {
	for row := 0; row < g.Config.Row; row++ {
		for col := 0; col < g.Config.Column; col++ {
			if grid[row][col] == g.Config.BlankIcon {
				return false
			}
		}
	}
	return true
}
