package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*g10000001])

type g10000001 struct {
	Config             c10000001
	RandByWeight       *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWild *utils.RandomWeightPicker[int16, int]
}

type c10000001 struct {
	Row         int
	Column      int
	Pattern     [][]basic.Position
	PayoutTable map[int16]int16
	CommonIcon  int16
	SpecialProb int32
	IconWeight  map[int16]int
	WildIcon    int16 // wildId
}

func (g10000001) ID() int32 {
	return 10000001
}

func (m *g10000001) Line() int32 { // 线数
	return 10
}

func (m g10000001) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g10000001) Exception(code int32) string {
	return games.S10000001{}.Exception(code)
}

func (g *g10000001) Init(config []byte) {
	g.Config = utils.ParseYAML[c10000001](config)
	g.RandByWeight = utils.NewRandomWeightPicker(g.Config.IconWeight)
	noWildWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.WildIcon {
			noWildWeight[icon] = weight
		}
	}
	g.RandByWeightNoWild = utils.NewRandomWeightPicker(noWildWeight)
}

func (g g10000001) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (g *g10000001) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S10000001
	if rd.Int31n(100) < g.Config.SpecialProb {
		spin = g.genSpec(rd, true)
	} else {
		spin = g.genNorm(rd, false)
	}
	return &spin

}

func (g *g10000001) genNorm(rd *rand.Rand, isSpec bool) games.S10000001 {
	grid := g.RandByWeight.More(12, rd)
	spin := games.S10000001{
		Pages: [][]int16{grid},
		Spec:  isSpec,
	}
	spin.Pays, spin.Lines, spin.Tenfold = g.parsePayout(grid)
	return spin
}

func (g *g10000001) genSpec(rd *rand.Rand, isSpec bool) games.S10000001 {
	grids := make([]int16, 12)
	var pages [][]int16 // 存储所有旋转的格子
	var hit int16
	var pay int32
	var lines [][]int16 // 赢的线
	var tenfold bool
	grid := g.RandByWeight.One(rd)
	// 生成一个全部图标相同的棋盘
	for i := 0; i < 12; i++ {
		grids[i] = grid
	}
	//将第二列的棋盘（列索引为1） 图标设置为随机图标
	for i := 1; i < 12; i += 3 {
		randGrid := g.RandByWeight.One(rd)
		grids[i] = randGrid

	}
	pages = append(pages, grids)
	pay, lines, tenfold = g.parsePayout(grids)
	if pay > 0 {
		spin := games.S10000001{
			Tenfold: tenfold,
			Lines:   lines,
			Pays:    pay,
			Pages:   pages,
			Hit:     hit,
			Spec:    isSpec,
		}
		return spin
	}

	for {
		newGrid := make([]int16, len(grids))
		one := g.RandByWeight.One(rd)
		copy(newGrid, grids)
		for r := 0; r < g.Config.Row; r++ {
			newGrid[r*g.Config.Column] = one                   // 第一列
			newGrid[r*g.Config.Column+g.Config.Column-1] = one // 第三列
		}
		for i := 1; i < 12; i += 3 {
			newGrid[i] = g.RandByWeight.One(rd) //旋转中间列
		}
		pages = append(pages, newGrid) // 记录每次重转的格子
		// 检查是否有赢
		pay, lines, tenfold = g.parsePayout(newGrid)
		if pay > 0 {
			break // 有赢时停止
		}
	}
	//fmt.Println(pages)
	spin := games.S10000001{
		Tenfold: tenfold,
		Lines:   lines,
		Pays:    pay,
		Pages:   pages,
		Hit:     hit,
		Spec:    isSpec,
	}
	//spin.Pays, spin.Lines, spin.Tenfold = g.parsePayout(pages[len(pages)-1])
	return spin
}

func (g g10000001) parsePayout(grid []int16) (payout int32, lines [][]int16, tenfold bool) {
	isTen := g.checkTenfold(grid)
	var flag uint64
	for idx, pos := range g.Config.Pattern {
		x := pos[0].Index(int32(g.Config.Column))
		y := pos[1].Index(int32(g.Config.Column))
		z := pos[2].Index(int32(g.Config.Column))
		ico, pay := g.checkLine(grid[x], grid[y], grid[z])
		if pay > 0 && isTen {
			lines = append(lines, []int16{
				ico, int16(idx), pay * 10,
				int16(x), int16(y), int16(z),
			})
			payout += int32(pay * 10)
			flag |= 1<<x | 1<<y | 1<<z
		} else {
			lines = append(lines, []int16{
				ico, int16(idx), pay,
				int16(x), int16(y), int16(z),
			})
			payout += int32(pay)
			flag |= 1<<x | 1<<y | 1<<z
		}
	}
	// 如果所有格子都至少参与过一次命中，则 tenfold
	if flag == (1<<uint(g.Config.Row*g.Config.Column))-1 {
		tenfold = true
	}
	return
}

func (g g10000001) checkLine(a, b, c int16) (int16, int16) {
	cnt := map[int16]int{a: 1, b: 1, c: 1}
	delete(cnt, g.Config.WildIcon)
	// 全是万能
	if len(cnt) == 0 {
		return g.Config.WildIcon, g.Config.PayoutTable[g.Config.WildIcon]
	}
	// 只有一种非万能符号，万能补齐即可
	if len(cnt) == 1 {
		for k := range cnt {
			return k, g.Config.PayoutTable[k]
		}
	}
	// 其它情况不成线
	return -1, 0
}

func (g g10000001) checkTenfold(grid []int16) bool {
	var oneIcon int16
	found := false
	for i, icon := range grid {
		if icon != g.Config.WildIcon && i != 9 && i != 11 {
			oneIcon = grid[i]
			found = true
			break
		}
	}
	if !found {
		return true
	}
	for i, icon := range grid {
		if i == 9 || i == 11 {
			continue
		}
		if icon != oneIcon && icon != g.Config.WildIcon {
			return false
		}
	}
	return true
}

func (g g10000001) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(*games.S10000001)
	spin.GameId = g.ID()
	spin.Line = g.Line()
	spin.Pattern = g.Config.Pattern
	spin.Row = g.Config.Row
	spin.Column = g.Config.Column
	return spin
}

func (g g10000001) Rule() string {
	b, _ := json.Marshal(g.Config)
	return string(b)
}

func (g10000001) InputCoef(int32) int32 {
	return 100
}

func (m g10000001) MinPayout(ctl int32) int32 {
	return 0
}
