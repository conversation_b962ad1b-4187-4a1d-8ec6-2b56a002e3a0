package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*g400128])

type g400128 struct {
	Config                   c400128
	RandByWeight             *utils.RandomWeightPicker[int16, int]
	RandByWeightNoScatter    *utils.RandomWeightPicker[int16, int]
	RandByArrLockWeight      *utils.RandomWeightPicker[int16, int]
	RandByLockNumWeight      *utils.RandomWeightPicker[int16, int]
	RandByIconLockWeight     *utils.RandomWeightPicker[int16, int]
	RandByGoldCoinWeight     *utils.RandomWeightPicker[int, int]
	RandByExtraWeight        *utils.RandomWeightPicker[int16, int]
	RandByIconNormLockWeight *utils.RandomWeightPicker[int16, int]
}

type c400128 struct {
	GameID               int // 游戏ID
	Row                  int // 网格行数
	Column               int // 网格列数
	MaxPayout            int32
	WildIcon             int16
	ArrLockIcon          int16
	GoldPigCoin          int16
	PayoutTable          [][]int16
	SpecMulTable         [][]int32
	IconWeight           map[int16]int
	GoldCoinWeight       map[int]int
	GoldCoinPayoutWeight map[int]int32
	ArrLockWeight        map[int16]int
	LockNumWeight        map[int16]int
	IconLockWeight       map[int16]int
	ExtraWeight          map[int16]int
	IconNormLockWeight   map[int16]int
	FreeSpin             FreeSpin400128     // 免费旋转配置
	Pattern              [][]basic.Position // 连线模式
	MinLimit             map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

type FreeSpin400128 struct {
	Icon   int16
	Number int16
}

func (g *g400128) Init(config []byte) {
	g.Config = utils.ParseYAML[c400128](config)
	g.RandByWeight = utils.NewRandomWeightPicker(g.Config.IconWeight)
	noScatterWeight := make(map[int16]int)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.FreeSpin.Icon {
			noScatterWeight[icon] = weight
		}
	}
	g.RandByWeightNoScatter = utils.NewRandomWeightPicker(noScatterWeight)
	g.RandByArrLockWeight = utils.NewRandomWeightPicker(g.Config.ArrLockWeight)
	g.RandByLockNumWeight = utils.NewRandomWeightPicker(g.Config.LockNumWeight)
	g.RandByIconLockWeight = utils.NewRandomWeightPicker(g.Config.IconLockWeight)
	g.RandByGoldCoinWeight = utils.NewRandomWeightPicker(g.Config.GoldCoinWeight)
	g.RandByExtraWeight = utils.NewRandomWeightPicker(g.Config.ExtraWeight)
}

func (g400128) ID() int32 {
	return 400128
}

func (m g400128) Line() int32 { // 线数
	return 20
}

func (m g400128) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g400128) Exception(code int32) string {
	return "{}"
}

func (g *g400128) Spin(rd *rand.Rand) basic.ISpin {
	spin := games.S400128{
		IsFree: false,
	}
	//page := games.P400128{}
	page, s := g.generatePage(rd)
	allPay := int32(0)

	for _, line := range page.Lines {
		allPay += int32(line.Iwin.Win)
	}
	if page.IsGoldCoinWin {
		allPay += int32(page.SubGameInfos[0].CurWin)
	}
	page.Pays = allPay
	spin.IsFree = s.IsFree
	spin.Pays = page.Pays
	if spin.IsFree {
		count := 1
		extra, arrLock, startFreeGrid, startFreeGridSafeMask := g.genInitFreePage(rd)
		page.FreeInfo = belatra.FreeInfo{
			Total:   count,
			Remain:  1,
			Award:   1,
			Nesting: count,
			WildDir: map[string]any{"extra": extra, "freezeCounters": []int{0, 0, 0, 0, 0}, "prevFreezeCounters": []int{0, 0, 0, 0, 0}},
			AllWin:  0,
			FGAux:   0,
		}
		page.StartFreeGrid = startFreeGrid
		page.ArrLockNew = arrLock
		page.StartFGSafeMask = startFreeGridSafeMask
		page.StartFGBox = startFreeGrid
		mul := g.getSpecMulTable(extra)
		page.Mul = mul
		page.AfterFgBox = page.Grid
		page.IbetsBB = append(page.IbetsBB, g.generateIbetsBB(page, count))
		spin.Pages = append(spin.Pages, page)
		//启动免费轮次循环
		freeSpinCount := 5
		totalPays := int32(0)
		for i := 0; i < freeSpinCount; i++ {
			if i != 0 {
				mul++
			}
			count++
			onePagePay := int32(0)
			goldCoinPay := int32(0)
			freePage := g.generateFreePage(rd, spin.Pages[len(spin.Pages)-1], count)
			for _, line := range freePage.Lines {
				onePagePay += int32(line.Iwin.Win)
			}
			freePage.Mul = mul
			freePage.Pays = onePagePay * mul
			totalPays += freePage.Pays
			freePage.IbetsBB = append(freePage.IbetsBB, g.generateIbetsBB(freePage, count))
			freePage.FreeInfo = belatra.FreeInfo{
				Total:   count,
				Remain:  1,
				Award:   1,
				Nesting: count,
				WildDir: map[string]any{"extra": extra, "freezeCounters": []int{0, 0, 0, 0, 0}, "prevFreezeCounters": []int{0, 0, 0, 0, 0}},
				AllWin:  int(totalPays),
				FGAux:   0,
			}
			if mul != 1 && onePagePay != 0 {
				freePage.SubGameInfos = append(freePage.SubGameInfos, belatra.SubGameInfo{
					Category:      "Bonus1",
					Type:          "1",
					StartWin:      int(page.Pays) + int(onePagePay),
					PrevWin:       int(page.Pays) + int(onePagePay),
					CurWin:        int(totalPays),
					PaidWin:       -1,
					Attempt:       0,
					Av:            []int{},
					AttemptResult: 0,
					WinLevel:      0,
					Rule:          "",
					Add:           []interface{}{},
					OnlyToBD:      nil,
					WinSumNoMult:  onePagePay,
					WinSumMult:    freePage.Pays,
					Mult:          mul,
				})
			}
			if freePage.IsGoldCoinWin {
				goldCoinMul := g.getGoldCoinPayoutTable(freePage.PigCoinIcon)
				goldCoinPay = goldCoinMul * g.Line()
				freePage.SubGameInfos = append(freePage.SubGameInfos, belatra.SubGameInfo{
					Category:      "Bonus0",
					Type:          "0",
					StartWin:      0,
					PrevWin:       0,
					CurWin:        int(goldCoinPay),
					PaidWin:       -1,
					Attempt:       0,
					Av:            freePage.PigCoinIcon,
					AttemptResult: 0,
					WinLevel:      0,
					Rule:          "",
					Add:           []interface{}{},
					OnlyToBD:      nil,
					CoinsWin:      int(goldCoinPay),
					CoinsMulWin:   int(goldCoinPay * mul),
				})
				goldCoinPayMul := goldCoinPay * mul
				freePage.Pays += goldCoinPayMul
				totalPays += goldCoinPayMul
			}
			spin.Pages = append(spin.Pages, freePage)
			spin.Pays = totalPays

			if i+1 == freeSpinCount && g.checkBreakSpin(spin.Pages[len(spin.Pages)-1].ArrLockNew) {
				freeSpinCount++
			}
			if freePage.AddSpin {
				freeSpinCount += freePage.AddSpinNu
			}
			if !g.checkBreakSpin(freePage.ArrLockNew) {
				lastPagePay := int32(0)
				lastPage := g.generateLastFreePage(rd, spin.Pages[len(spin.Pages)-1])
				for _, line := range lastPage.Lines {
					lastPagePay += int32(line.Iwin.Win)
				}
				lastPage.Mul = mul + 1
				lastPage.Pays = lastPagePay * lastPage.Mul
				totalPays += lastPage.Pays
				lastPage.IbetsBB = append(lastPage.IbetsBB, g.generateIbetsBB(lastPage, count))

				lastPage.FreeInfo = belatra.FreeInfo{
					Total:   count,
					Remain:  0,
					Award:   0,
					Nesting: count,
					WildDir: map[string]any{"extra": nil, "freezeCounters": []int{0, 0, 0, 0, 0}, "prevFreezeCounters": []int{0, 0, 0, 0, 0}},
					AllWin:  int(totalPays),
					FGAux:   0,
				}
				lastPage.SubGameInfos = append(lastPage.SubGameInfos, belatra.SubGameInfo{
					Category:      "Bonus1",
					Type:          "1",
					StartWin:      int(page.Pays) + int(lastPagePay),
					PrevWin:       int(page.Pays) + int(lastPagePay),
					CurWin:        int(totalPays),
					PaidWin:       -1,
					Attempt:       0,
					Av:            []int{},
					AttemptResult: 0,
					WinLevel:      0,
					Rule:          "",
					Add:           []interface{}{},
					OnlyToBD:      nil,
					WinSumNoMult:  lastPagePay,
					WinSumMult:    lastPage.Pays,
					Mult:          lastPage.Mul,
				})
				spin.Pages = append(spin.Pages, lastPage)
				spin.Pays = totalPays
				break
			}
			//保护性退出
			if freeSpinCount > 45 {
				break
			}
		}

	} else {
		spin.Pages = append(spin.Pages, page)
	}
	return &spin
}

func (g *g400128) generatePage(rd *rand.Rand) (games.P400128, games.S400128) {
	s := games.S400128{}
	arrLock := []bool{false, false, false, false, false}
	isGoldCoinWin := false
	pigCoinIcon := []int{}
	subGameInfos := belatra.SubGameInfo{}
	prevSafeMask := g.makeGrid()
	cols, rows := g.Config.Column, g.Config.Row
	icons := g.RandByWeight.More(cols*rows, rd)
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = icons[row*g.Config.Column+col]
		}
	}
	InitIcons := g.RandByWeight.More(cols*rows, rd)
	startInitGridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		startInitGridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			startInitGridRows[row][col] = InitIcons[row*g.Config.Column+col]
		}
	}
	useLocker := false
	safeMask := g.generateNormSafeMask(rd, prevSafeMask)
	gridRowsView := cloneSlice(gridRows)
	oneIcon := g.RandByIconLockWeight.One(rd)
	colMap := map[int]bool{}
	for i, safe := range safeMask {
		for j := range safe {
			if safeMask[i][j] == 1 {
				colMap[j] = true
				useLocker = true
				gridRows[i][j] = g.Config.ArrLockIcon
				gridRowsView[i][j] = oneIcon
			}
		}
	}
	for i := 0; i < g.Config.Row; i++ {
		for k, v := range colMap {
			if v && gridRows[i][k] == 11 {
				noScatterIcon := g.RandByWeightNoScatter.One(rd)
				gridRows[i][k] = noScatterIcon
				gridRowsView[i][k] = noScatterIcon
			}
		}
	}
	isGoldCoinWin, pigCoinIcon, _, _ = g.checkGoldCoinWin(rd, oneIcon, safeMask)
	if isGoldCoinWin && useLocker {
		goldCoinMul := g.getGoldCoinPayoutTable(pigCoinIcon)
		goldCoinPay := goldCoinMul * g.Line()
		subGameInfos = belatra.SubGameInfo{
			Category:      "Bonus0",
			Type:          "0",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        int(goldCoinPay),
			PaidWin:       -1,
			Attempt:       0,
			Av:            pigCoinIcon,
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "",
			Add:           []interface{}{},
			OnlyToBD:      nil,
			CoinsWin:      int(goldCoinPay),
			CoinsMulWin:   int(goldCoinPay),
		}
	}

	ibet := g.generateIbetsChange(gridRowsView, arrLock, arrLock, prevSafeMask, safeMask)
	ibets := []belatra.IbetsChange{ibet}
	count := 0
	for _, gridRow := range gridRows {
		for _, icon := range gridRow {
			if icon == g.Config.FreeSpin.Icon {
				count++
			}
			if count >= 3 {
				s.IsFree = true
			}
		}
	}
	lineInfo := g.getPayout(gridRowsView)
	return games.P400128{
			Grid:          gridRows,
			GridView:      gridRowsView,
			IsGoldCoinWin: isGoldCoinWin,
			ArrLockNew:    arrLock,
			SafeMask:      safeMask,
			IbetsChange:   ibets,
			Lines:         lineInfo,
			IncutId:       oneIcon,
			SubGameInfos:  append([]belatra.SubGameInfo{}, subGameInfos)},
		s
}

func (g g400128) makeGrid() [][]int16 {
	gridRows := make([][]int16, g.Config.Row)
	for row := 0; row < g.Config.Row; row++ {
		gridRows[row] = make([]int16, g.Config.Column)
		for col := 0; col < g.Config.Column; col++ {
			gridRows[row][col] = 0
		}
	}
	return gridRows
}

func (g g400128) getPayout(grid [][]int16) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range g.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 4 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = grid[row][col]
		}
		if ok, payout, online := g.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   20,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		}
	}
	return linesInfo
}

func (g g400128) checkLine(symbols []int16) (bool, int64, []int16) {
	if len(symbols) < 3 {
		return false, 0, nil
	}

	// 初始化匹配图标和计数
	matchIcon := symbols[0]
	count := 1
	wildCount := 0

	// 如果第一个图标是 WildIcon，寻找第一个非 WildIcon 作为匹配基准
	if matchIcon == g.Config.WildIcon {
		wildCount = 1
		for i := 1; i < len(symbols); i++ {
			if symbols[i] != g.Config.WildIcon {
				matchIcon = symbols[i] // 找到第一个非 WildIcon
				break
			}
			wildCount++
		}
		// 如果全是 WildIcon，按 WildIcon 赔付
		if wildCount >= 3 {
			payout := g.getPayoutTable(symbols, wildCount)
			online := make([]int16, len(symbols))
			copy(online, symbols[:wildCount])
			for i := wildCount; i < len(symbols); i++ {
				if online[i] == 0 {
					online[i] = 127
				}
			}
			return true, int64(payout), online
		}
	}

	// 从后续图标开始统计连续匹配
	for i := 1; i < len(symbols); i++ {
		if symbols[i] == matchIcon || symbols[i] == g.Config.WildIcon {
			count++
		} else {
			break
		}
	}
	if count >= 3 {
		payout := g.getPayoutTable(symbols, count)
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		for i := count; i < len(symbols); i++ {
			if online[i] == 0 {
				online[i] = 127
			}
		}
		return true, int64(payout), online
	}
	return false, 0, nil
}

func (g g400128) getPayoutTable(symbols []int16, count int) int16 {
	// 确定实际的匹配图标
	matchIcon := symbols[0]

	// 如果第一个图标是wild符号，寻找第一个非wild符号作为匹配基准
	if matchIcon == g.Config.WildIcon {
		for i := 1; i < len(symbols) && i < count; i++ {
			if symbols[i] != g.Config.WildIcon {
				matchIcon = symbols[i]
				break
			}
		}
		// 如果全是wild符号，使用wild符号的赔率
		if matchIcon == g.Config.WildIcon {
			for _, payout := range g.Config.PayoutTable {
				if payout[0] == g.Config.WildIcon && int(payout[1]) == count {
					return payout[2]
				}
			}
		}
	}

	// 使用确定的匹配图标查找赔率
	for _, payout := range g.Config.PayoutTable {
		if payout[0] == matchIcon && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

func (g g400128) getGoldCoinPayoutTable(GoldCoin []int) int32 {
	goldCoin := int32(0)
	for _, coin := range GoldCoin {
		if value, exists := g.Config.GoldCoinPayoutWeight[coin]; exists {
			goldCoin += value // 累加找到的 value
		}
	}
	return goldCoin
}
func (g g400128) getSpecMulTable(extras []int16) int32 {
	mul := int32(1)
	for _, SpecMul := range g.Config.SpecMulTable {
		for _, extra := range extras {
			if int32(extra) == SpecMul[0] {
				mul += SpecMul[1]
			}
		}
	}
	return mul
}

func (g g400128) generateNormSafeMask(rd *rand.Rand, prevSafeMask [][]int16) (safeMask [][]int16) {
	safeMask = cloneSlice(prevSafeMask)
	for i := 0; i < g.Config.Column; i++ {
		if g.RandByArrLockWeight.One(rd) == 1 {
			lockIcon := g.rollLock(rd)
			for j, icon := range lockIcon {
				safeMask[j][i] = icon
			}
		}
	}
	return safeMask
}
func (g g400128) generateSafeMask(rd *rand.Rand, arrLock []bool, prevSafeMask [][]int16, lastPage bool) (arrLockNew []bool, safeMask [][]int16) {
	if len(prevSafeMask) == 0 {
		prevSafeMask = make([][]int16, g.Config.Row)
		for row := 0; row < g.Config.Row; row++ {
			prevSafeMask[row] = make([]int16, g.Config.Column)
		}
	}
	safeMask = cloneMaskSlice(prevSafeMask)
	arrLockNew = make([]bool, len(arrLock))
	copy(arrLockNew, arrLock)
	// 更新 safeMask 的单列
	for i, lock := range arrLock {
		if lock {
			if prevSafeMask[0][i] == 0 {
				for j, _ := range prevSafeMask {
					if prevSafeMask[j][i] == 1 {
						safeMask[j][i] = 0
						break
					}
				}
			} else if prevSafeMask[0][i] == 1 {
				for j, _ := range prevSafeMask {
					if prevSafeMask[j][i] == 0 {
						safeMask[j][i] = 1
						break
					} else if j == len(prevSafeMask)-1 {
						safeMask[0][i] = 0
						break
					}
				}
			}
			if safeMask[3][i] == 1 && safeMask[2][i] == 0 {
				arrLockNew[i] = false
			}
		} else {
			if prevSafeMask[3][i] == 1 && prevSafeMask[2][i] == 0 {
				safeMask[3][i] = 0
			}
			if g.RandByArrLockWeight.One(rd) == 1 && !lastPage {
				lockIcon := g.rollLock(rd)
				for j, icon := range lockIcon {
					safeMask[j][i] = icon
				}
				if safeMask[3][i] == 1 && safeMask[2][i] == 0 {
					arrLockNew[i] = false
				} else {
					arrLockNew[i] = true
				}
			}
		}
	}
	return arrLockNew, safeMask
}

func (g *g400128) generateIbetsChange(gridRowView [][]int16, ArrLock []bool, ArrLockNew []bool, PrevSafeMask [][]int16, SafeMask [][]int16) belatra.IbetsChange {
	return belatra.IbetsChange{
		Bet:           20,
		GateId:        0,
		GateInId:      0,
		PrefixId:      3,
		SeriesPlaying: 0,
		SeriesWin:     0,
		PartWins:      []int{0},
		SeedInfo: belatra.SeedInfo{
			Seedstart:    2137495, // 可使用 rd.Intn() 随机化
			Uses:         5,
			Prevuses:     0,
			Win1:         0,
			WinK:         0,
			Info:         nil,
			FgCountTotal: []int{0},
			NHeirTotal:   0,
		},
		SeedInfoHeir: belatra.SeedInfoHeir{
			Counter: 0,
			HasHeir: 0,
		},
		StopBox:         gridRowView,
		InfoBeforeFg:    nil,
		StartFGSafeMask: nil,
		Nustops:         []any{nil, nil, nil, nil, nil},
		SafeIncutId:     nil,
		PrevSafeIncutId: nil,
		PrevSafeMask:    nil,
		SafeMask:        SafeMask,
		DirShift:        1,
		StartFGBox:      nil,
		ArrLock:         nil,
		ArrLockNew:      ArrLockNew,
		UpN:             0,
		UpNNext:         0,
		SpecMult:        1,
		ExtrimeUp:       nil,
		NeedBreakSeries: false,
		StartBox: [][]int{
			{5, 1, 3, 1, 2},
			{5, 2, 3, 4, 2},
			{5, 2, 4, 11, 2},
			{6, 8, 0, 4, 8},
		},
		SeriesStatus: 1,
	}
}

func (g *g400128) generateIbetsBB(page games.P400128, count int) belatra.IbetsBB {
	return belatra.IbetsBB{
		Bet:       20,
		GateId:    0,
		GateInId:  0,
		PrefixId:  1,
		SeriesWin: 30,
		PartWins:  []int{30},
		SeedInfo: belatra.SeedInfo{
			Seedstart:    2137495, // 可使用 rd.Intn() 随机化
			Uses:         5,
			Prevuses:     int(g.Line()),
			Win1:         int(page.Pays),
			WinK:         int(page.Pays) / int(g.Line()),
			Info:         nil,
			FgCountTotal: []int{0},
			NHeirTotal:   0,
		},
		SeedInfoHeir: belatra.SeedInfoHeir{
			Counter: 0,
			HasHeir: 0,
		},
		StopBox:         page.Grid,
		InfoBeforeFg:    nil,
		StartFGSafeMask: page.StartFGSafeMask,
		Nustops:         []any{nil, nil, nil, nil, nil},
		SafeIncutId:     nil,
		PrevSafeIncutId: nil,
		PrevSafeMask:    page.PrevSafeMask,
		SafeMask:        page.SafeMask,
		DirShift:        utils.IfElse(count == 2, 0, 1),
		StartFGBox:      page.StartFreeGrid,
		ArrLock:         page.ArrLock,
		ArrLockNew:      page.ArrLockNew,
		AfterFgBox:      page.Grid,
		UpN:             0,
		UpNNext:         0,
		SpecMult:        page.Mul,
		ExtrimeUp:       nil,
		NeedBreakSeries: false,
		StartBox:        nil,
		SeriesStatus:    1,
	}
}

func cloneMaskSlice(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}

func (g *g400128) generateExtra(rd *rand.Rand) []int16 {
	extra := make([]int16, g.Config.Column)
	hasNine := false

	// Generate initial values
	for i := range extra {
		extra[i] = g.RandByExtraWeight.One(rd)
		if extra[i] == 9 {
			hasNine = true
		}
	}

	// If no 9 is found and length is 5, set a random index to 9
	if !hasNine && len(extra) == 5 {
		randomIndex := rd.Intn(5)
		extra[randomIndex] = 9
	}

	return extra
}

func (g *g400128) generateFreePage(rd *rand.Rand, freePage games.P400128, count int) games.P400128 {
	pigCoinIcon := []int{}
	isGoldCoinWin := false
	addSpin := false
	addSpinNu := 0
	dirShift := int16(0)
	if count < 3 {
		//第一轮不做下落操作
		gridRows := cloneMaskSlice(freePage.StartFGBox)
		cols, rows := g.Config.Column, g.Config.Row
		icons := g.RandByWeightNoScatter.More(cols*rows, rd)
		oneIcon := g.RandByIconLockWeight.One(rd)
		isGoldCoinWin, pigCoinIcon, addSpin, addSpinNu = g.checkGoldCoinWin(rd, oneIcon, freePage.StartFGSafeMask)
		for row := 0; row < g.Config.Row; row++ {
			for col := 0; col < g.Config.Column; col++ {
				if gridRows[row][col] != g.Config.ArrLockIcon {
					gridRows[row][col] = icons[row*g.Config.Column+col]
				}
			}
		}
		gridRowsView := cloneSlice(gridRows)
		for row := 0; row < g.Config.Row; row++ {
			for col := 0; col < g.Config.Column; col++ {
				if gridRows[row][col] == g.Config.ArrLockIcon {
					gridRowsView[row][col] = oneIcon
				}
			}
		}
		linesInfo := g.getPayout(gridRowsView)
		return games.P400128{
			Grid:          gridRows,
			GridView:      gridRowsView,
			StartFreeGrid: freePage.StartFreeGrid,
			Lines:         linesInfo,
			PrevSafeMask:  freePage.PrevSafeMask,
			SafeMask:      freePage.StartFGSafeMask,
			ArrLock:       freePage.ArrLockNew,
			ArrLockNew:    freePage.ArrLockNew,
			AfterFgBox:    freePage.AfterFgBox,
			IncutId:       oneIcon,
			SafeIncutId:   oneIcon,
			AddSpin:       addSpin,
			AddSpinNu:     addSpinNu,
			DirShift:      dirShift,
			PigCoinIcon:   pigCoinIcon,
			IsGoldCoinWin: isGoldCoinWin,
		}
	}
	//cols, rows := g.Config.Column, g.Config.Row
	//icons := g.RandByWeight.More(cols*rows, rd)
	oneIcon := g.RandByIconLockWeight.One(rd)
	//gridRows := make([][]int16, g.Config.Row)
	//gridRowsView := make([][]int16, g.Config.Row)
	var gridRows [][]int16
	var safeMask [][]int16
	var arrLockNew []bool
	switch freePage.AddSpin {
	case true:
		gridRows, safeMask, arrLockNew = g.freeSpinIconUp(rd, freePage.AddSpinNu, freePage.ArrLockNew, freePage.SafeMask, freePage.Grid)
		isGoldCoinWin, pigCoinIcon, addSpin, addSpinNu = g.checkGoldCoinWin(rd, oneIcon, safeMask)
		dirShift = -1
	case false:
		dirShift = 1
		arrLockNew, safeMask = g.generateSafeMask(rd, freePage.ArrLockNew, freePage.SafeMask, false)
		gridRows = g.freeSpinIconDown(rd, freePage.ArrLockNew, arrLockNew, safeMask, freePage.Grid)
		isGoldCoinWin, pigCoinIcon, addSpin, addSpinNu = g.checkGoldCoinWin(rd, oneIcon, safeMask)
	}
	gridRowsView := cloneSlice(gridRows)
	for row := 0; row < g.Config.Row; row++ {
		for col := 0; col < g.Config.Column; col++ {
			if gridRows[row][col] == g.Config.ArrLockIcon {
				gridRowsView[row][col] = oneIcon
			}
		}
	}
	linesInfo := g.getPayout(gridRowsView)
	return games.P400128{
		Grid:            gridRows,
		GridView:        gridRowsView,
		Lines:           linesInfo,
		ArrLock:         freePage.ArrLockNew,
		PrevSafeMask:    freePage.SafeMask,
		StartFreeGrid:   freePage.Grid,
		ArrLockNew:      arrLockNew,
		SafeMask:        utils.IfElse(g.checkBreakSpin(freePage.ArrLockNew) && g.checkBreakSpin(freePage.ArrLock), safeMask, nil),
		AfterFgBox:      freePage.AfterFgBox,
		PrevSafeIncutId: freePage.SafeIncutId,
		IncutId:         oneIcon,
		SafeIncutId:     oneIcon,
		AddSpin:         addSpin,
		AddSpinNu:       addSpinNu,
		DirShift:        dirShift,
		PigCoinIcon:     pigCoinIcon,
		IsGoldCoinWin:   isGoldCoinWin,
	}
}

func (g *g400128) generateLastFreePage(rd *rand.Rand, freePage games.P400128) games.P400128 {
	oneIcon := g.RandByWeight.One(rd)
	arrLockNew, safeMask := g.generateSafeMask(rd, freePage.ArrLockNew, freePage.SafeMask, true)
	gridRows := g.freeSpinIconDown(rd, freePage.ArrLockNew, arrLockNew, safeMask, freePage.Grid)
	gridRowsView := cloneSlice(gridRows)
	linesInfo := g.getPayout(gridRowsView)
	ibet := g.generateIbetsChange(gridRowsView, freePage.ArrLockNew, arrLockNew, freePage.SafeMask, safeMask)
	ibets := []belatra.IbetsChange{ibet}
	return games.P400128{
		Grid:            gridRows,
		GridView:        gridRowsView,
		Lines:           linesInfo,
		ArrLock:         freePage.ArrLockNew,
		PrevSafeMask:    freePage.SafeMask,
		StartFreeGrid:   freePage.Grid,
		ArrLockNew:      arrLockNew,
		SafeMask:        nil,
		AfterFgBox:      freePage.AfterFgBox,
		PrevSafeIncutId: freePage.SafeIncutId,
		IncutId:         oneIcon,
		SafeIncutId:     oneIcon,
		IbetsChange:     ibets,
		DirShift:        1,
	}
}

func (g *g400128) genInitFreePage(rd *rand.Rand) ([]int16, []bool, [][]int16, [][]int16) {
	extra := g.generateExtra(rd)
	arrLock := []bool{false, false, false, false, false}
	startFreeGrid := make([][]int16, g.Config.Row)
	for i := 0; i < g.Config.Column; i++ {
		if extra[i] == g.Config.ArrLockIcon {
			arrLock[i] = true
		}
	}
	for i := 0; i < g.Config.Row; i++ {
		startFreeGrid[i] = make([]int16, g.Config.Column)
		for j := 0; j < g.Config.Column; j++ {
			if arrLock[j] {
				startFreeGrid[i][j] = g.Config.ArrLockIcon
			} else {
				startFreeGrid[i][j] = g.RandByWeight.One(rd)
			}
		}
	}
	startFreeGridSafeMask := make([][]int16, g.Config.Row)

	for i := 0; i < g.Config.Row; i++ {
		startFreeGridSafeMask[i] = make([]int16, g.Config.Column)
		for j := 0; j < g.Config.Column; j++ {
			if arrLock[j] {
				startFreeGridSafeMask[i][j] = 1
			} else {
				startFreeGridSafeMask[i][j] = 0
			}
		}
	}
	//viewFreeGrid := cloneMaskSlice(startFreeGrid)
	//viewIcon := g.RandByWeight.One(rd)
	//for i := 0; i < g.Config.Row; i++ {
	//	for j := 0; j < g.Config.Column; j++ {
	//		if viewFreeGrid[i][j] == g.Config.ArrLockIcon {
	//			viewFreeGrid[i][j] = viewIcon
	//		}
	//	}
	//}
	return extra, arrLock, startFreeGrid, startFreeGridSafeMask
}

func (g *g400128) rollLock(rd *rand.Rand) []int16 {
	lockNum := g.RandByLockNumWeight.One(rd)
	result := make([]int16, 4)

	// 随机决定填充方向（0：从开头，1：从结尾）
	direction := rd.Intn(2)

	// 根据方向计算起始和结束索引
	start := 0
	end := int(lockNum)
	if direction == 1 {
		start = 4 - int(lockNum)
		end = 4
	}

	// 在选定范围内填充1
	for i := start; i < end; i++ {
		result[i] = 1
	}

	return result
}

func (g *g400128) checkBreakSpin(arrLockNew []bool) bool {
	for _, v := range arrLockNew {
		if v {
			return true
		}
	}
	return false
}

func (g *g400128) checkGoldCoinWin(rd *rand.Rand, oneIcon int16, safeMask [][]int16) (isGoldCoinWin bool, pigCoinIcon []int, addSpin bool, addSpinNu int) {
	if oneIcon == 10 {
		coinCount := 0
		addSpinNu = 0
		addSpin = false
		isGoldCoinWin = true
		for _, icons := range safeMask {
			for _, icon := range icons {
				if icon == 1 {
					coinCount++
				}
			}
		}
		pigCoinIcon = g.RandByGoldCoinWeight.More(coinCount, rd)
		for _, v := range pigCoinIcon {
			if v == 12 {
				addSpinNu++
				addSpin = true
			}
		}
	}
	return isGoldCoinWin, pigCoinIcon, addSpin, addSpinNu
}

func (g *g400128) freeSpinIconDown(rd *rand.Rand, arrLock []bool, arrLockNew []bool, safeMask [][]int16, gridRows [][]int16) [][]int16 {
	newGridRows := cloneMaskSlice(gridRows)
	icons := g.RandByWeightNoScatter.More(g.Config.Row*g.Config.Column, rd)
	for i, ok := range arrLock {
		if ok {
			for j := 3; j > 0; j-- {
				newGridRows[j][i] = newGridRows[j-1][i]
				if j == 1 && safeMask[j-1][i] != 1 {
					newGridRows[j-1][i] = g.RandByWeight.One(rd)
				} else {
					newGridRows[j-1][i] = g.Config.ArrLockIcon
				}
			}
		} else {
			for j := 0; j < g.Config.Row; j++ {
				if arrLockNew[i] && safeMask[j][i] == 1 {
					newGridRows[j][i] = g.Config.ArrLockIcon
				} else {
					newGridRows[j][i] = icons[j*g.Config.Column+i]
				}
			}
		}
	}
	return newGridRows
}

func (g *g400128) freeSpinIconUp(rd *rand.Rand, addSpinNu int, arrLockNew []bool, safeMask [][]int16, gridRows [][]int16) ([][]int16, [][]int16, []bool) {
	newGridRows := cloneMaskSlice(gridRows)
	newSafeMask := cloneMaskSlice(safeMask)
	icons := g.RandByWeightNoScatter.More(g.Config.Row*g.Config.Column, rd)
	for n := 0; n < addSpinNu; n++ {
		for i, ok := range arrLockNew {
			if newSafeMask[0][i] == 1 && newSafeMask[1][i] == 1 && newSafeMask[g.Config.Row-1][i] != 1 {
				for j := 0; j < g.Config.Row-1; j++ {
					newSafeMask[j][i] = newSafeMask[j+1][i]
					newGridRows[j][i] = newGridRows[j+1][i]
				}
				newGridRows[g.Config.Row-1][i] = g.RandByWeight.One(rd)
			} else if newSafeMask[0][i] == 1 && newSafeMask[g.Config.Row-1][i] == 1 {
				newSafeMask[g.Config.Row-1][i] = 0
				newGridRows[g.Config.Row-1][i] = g.RandByWeight.One(rd)
			} else if newSafeMask[0][i] != 1 && newSafeMask[g.Config.Row-1][i] == 1 {
				for j := 0; j < g.Config.Row-1; j++ {
					newSafeMask[j][i] = newSafeMask[j+1][i]
					newGridRows[j][i] = newGridRows[j+1][i]
				}
			}
			if newSafeMask[g.Config.Row-1][i] == 1 {
				arrLockNew[i] = true
			}
			if !ok {
				for j := 0; j < g.Config.Row; j++ {
					newGridRows[j][i] = icons[j*g.Config.Column+i]

				}
			}
		}
	}

	return newGridRows, newSafeMask, arrLockNew
}

func (g *g400128) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (g *g400128) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400128)
	s.GameId = g.ID()
	s.Line = g.Line()

	return s
}

func (g400128) Rule() string {
	gs := map[string]any{
		"gamegroup":             "base",
		"doubleAssortment":      []string{"off"},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         []int{1, 2, 3, 4, 5, 10, 15, 20, 25, 50, 125, 250},
		"denomAssortment_cents": []int{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]interface{}{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus": map[string]interface{}{
			"buyTotalBetK": 100,
			"wasBuy":       0,
		},
		"outRatesVolatility":    nil,
		"placedbet":             20,
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            20,
		"betPerLine":            1,
		"nlines":                20,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 5000,
		"analInfo": map[string]interface{}{
			"formula": map[string]interface{}{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]interface{}{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles": [][]int{
				{2, 2, 2, 2, 2},
				{1, 1, 1, 1, 1},
				{3, 3, 3, 3, 3},
				{0, 0, 0, 0, 0},
				{0, 1, 2, 1, 0},
				{3, 2, 1, 2, 3},
				{1, 2, 3, 2, 1},
				{2, 1, 0, 1, 2},
				{3, 3, 1, 3, 3},
				{0, 0, 2, 0, 0},
				{1, 1, 3, 1, 1},
				{2, 2, 0, 2, 2},
				{1, 1, 0, 1, 1},
				{2, 2, 3, 2, 2},
				{0, 0, 1, 0, 0},
				{3, 3, 2, 3, 3},
				{0, 1, 0, 1, 0},
				{3, 2, 3, 2, 3},
				{1, 0, 1, 0, 1},
				{2, 3, 2, 3, 2},
				{0, 1, 1, 1, 0},
				{3, 2, 2, 2, 3},
				{2, 3, 3, 3, 2},
				{1, 0, 0, 0, 1},
				{2, 1, 2, 1, 2},
				{1, 2, 1, 2, 1},
				{0, 3, 3, 3, 0},
				{3, 0, 0, 0, 3},
				{1, 2, 2, 2, 1},
				{2, 1, 1, 1, 2},
				{0, 3, 0, 3, 0},
				{3, 0, 3, 0, 3},
				{1, 3, 3, 3, 1},
				{2, 0, 0, 0, 2},
				{0, 2, 0, 2, 0},
				{3, 1, 3, 1, 3},
				{1, 3, 1, 3, 1},
				{2, 0, 2, 0, 2},
				{0, 0, 3, 0, 0},
				{3, 3, 0, 3, 3},
				{1, 1, 2, 1, 1},
				{2, 2, 1, 2, 2},
				{0, 2, 2, 2, 0},
				{3, 1, 1, 1, 3},
				{1, 0, 3, 0, 1},
				{2, 3, 0, 3, 2},
				{0, 3, 1, 3, 0},
				{3, 0, 2, 0, 3},
				{1, 3, 2, 3, 1},
				{2, 0, 1, 0, 2},
			},
			"symbolNames": []string{
				"richy_hog", "dog", "piggy", "cat", "gold", "diamond", "cash", "dollar", "sigar", "safe", "golden_hog", "bonus", "bonus_1", "factor_1", "factor_2", "factor_5", "factor_10", "factor_25", "factor_50", "factor_100", "factor_500", "factor_1000", "factor_2500", "add_mul_1", "add_mul_2", "add_mul_3", "add_mul_5", "police_control", "empty",
			},
			"baseReels": [][]int{
				{1, 2, 3, 4, 8, 8, 8, 8, 0, 5, 6, 7, 8, 11, 1, 1, 6, 6, 6, 1, 2, 3, 9, 9, 9, 9, 4, 8, 8, 8, 8, 0, 5, 6, 7, 8, 11, 1, 1, 6, 6, 6, 0, 2, 2, 2, 2, 1, 1, 1, 3, 3, 5, 5, 5, 5, 6, 6, 8, 1, 1, 1, 1, 3, 3, 3, 0, 4, 4, 4, 5, 5, 5, 6, 8, 8, 5, 5, 11, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 7, 7, 6, 6, 6, 6, 7, 7, 7, 0, 5, 8, 8, 8},
				{1, 8, 8, 8, 8, 11, 2, 7, 7, 7, 7, 3, 6, 6, 6, 6, 4, 5, 5, 1, 8, 8, 9, 9, 9, 9, 8, 8, 11, 2, 7, 7, 7, 7, 3, 6, 6, 6, 6, 4, 5, 5, 5, 5, 0, 1, 2, 3, 4, 11, 1, 1, 1, 2, 2, 8, 8, 8, 3, 3, 7, 7, 7, 4, 4, 6, 6, 6, 5, 5, 1, 8, 2, 7, 6, 5, 4, 3, 11, 1, 1, 1, 1, 5, 2, 2, 2, 2, 6, 3, 3, 3, 3, 7, 4, 4, 4, 4, 8, 0, 3, 3, 3, 2, 2, 2, 1},
				{1, 1, 5, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 1, 5, 5, 9, 9, 9, 9, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 11, 2, 2, 2, 3, 5, 5, 5, 6, 7, 7, 7, 7, 0, 8, 8, 8, 8, 2, 2, 3, 3, 3, 4, 4, 1, 2, 2, 2, 2, 4, 4, 4, 4, 5, 0, 1, 1, 1, 3, 3, 11, 8, 7, 7, 2, 3, 4, 5, 6, 6, 3, 2, 2, 1, 7, 6, 6, 5, 5, 11, 5, 5, 4, 4},
				{5, 5, 11, 4, 4, 0, 3, 3, 1, 1, 1, 4, 11, 4, 6, 6, 7, 7, 5, 5, 5, 5, 9, 9, 9, 9, 4, 4, 0, 3, 3, 1, 1, 1, 4, 11, 4, 6, 6, 7, 7, 0, 1, 2, 2, 3, 3, 4, 4, 4, 5, 6, 6, 4, 4, 1, 5, 5, 2, 6, 6, 3, 3, 7, 7, 4, 4, 2, 2, 2, 2, 1, 1, 2, 2, 8, 8, 3, 3, 0, 3, 8, 8, 4, 4, 4, 11, 5, 5, 3, 3, 6, 6, 2, 2, 7, 7, 1, 1, 8, 8, 1, 1, 5, 5, 5, 2, 2, 2, 6, 6, 7, 3, 4, 4},
				{1, 1, 2, 2, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 11, 1, 1, 2, 9, 9, 9, 9, 2, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 11, 5, 6, 6, 1, 1, 1, 5, 5, 11, 2, 2, 6, 3, 0, 7, 7, 4, 4, 8, 8, 8, 5, 2, 2, 2, 2, 6, 6, 6, 1, 1, 1, 1, 2, 2, 5, 3, 3, 3, 0, 6, 4, 4, 8, 8, 8, 8, 11, 5, 5, 1, 2, 2, 8, 8, 3, 3, 7, 7, 4, 4, 6, 6, 11, 7, 6, 5, 4},
			},
			"freeReels": [][]int{{1, 2, 3, 4, 8, 8, 8, 8, 0, 5, 6, 7, 8, 1, 1, 6, 6, 6, 1, 2, 3, 4, 9, 9, 9, 9, 8, 8, 8, 8, 0, 5, 6, 7, 8, 1, 1, 6, 6, 6, 0, 2, 2, 2, 2, 1, 1, 1, 3, 3, 5, 5, 5, 5, 6, 6, 8, 1, 1, 1, 1, 3, 3, 3, 0, 4, 4, 4, 5, 5, 5, 6, 8, 8, 5, 5, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 7, 7, 6, 6, 6, 6, 7, 7, 7, 0, 5, 8, 8, 8},
				{1, 8, 8, 8, 8, 2, 7, 7, 7, 7, 3, 6, 6, 6, 6, 4, 5, 5, 1, 8, 8, 8, 9, 9, 9, 9, 8, 2, 7, 7, 7, 7, 3, 6, 6, 6, 6, 4, 5, 5, 5, 5, 0, 1, 2, 3, 4, 1, 1, 1, 2, 2, 8, 8, 8, 3, 3, 7, 7, 7, 4, 4, 6, 6, 6, 5, 5, 1, 8, 2, 7, 6, 5, 4, 3, 1, 1, 1, 1, 5, 2, 2, 2, 2, 6, 3, 3, 3, 3, 7, 4, 4, 4, 4, 8, 0, 3, 3, 3, 2, 2, 2, 1},
				{1, 1, 5, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 1, 5, 9, 9, 9, 9, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 2, 2, 2, 3, 5, 5, 5, 6, 7, 7, 7, 7, 0, 8, 8, 8, 8, 2, 2, 3, 3, 3, 4, 4, 1, 2, 2, 2, 2, 4, 4, 4, 4, 5, 0, 1, 1, 1, 3, 3, 8, 7, 7, 2, 3, 4, 5, 6, 6, 3, 2, 2, 1, 7, 6, 6, 5, 5, 5, 5, 4, 4},
				{5, 5, 4, 4, 0, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 5, 5, 4, 4, 0, 9, 9, 9, 9, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 1, 2, 2, 3, 3, 4, 4, 4, 5, 6, 6, 4, 4, 1, 5, 5, 2, 6, 6, 3, 3, 7, 7, 4, 4, 2, 2, 2, 2, 1, 1, 2, 2, 8, 8, 3, 3, 0, 3, 8, 8, 4, 4, 4, 5, 5, 3, 3, 6, 6, 2, 2, 7, 7, 1, 1, 8, 8, 1, 1, 5, 5, 5, 2, 2, 2, 6, 6, 7, 3, 4, 4},
				{1, 1, 2, 2, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 1, 1, 2, 2, 9, 9, 9, 9, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 5, 6, 6, 1, 1, 1, 5, 5, 2, 2, 6, 3, 0, 7, 7, 4, 4, 8, 8, 8, 5, 2, 2, 2, 2, 6, 6, 6, 1, 1, 1, 1, 2, 2, 5, 3, 3, 3, 0, 6, 4, 4, 8, 8, 8, 8, 5, 5, 1, 2, 2, 8, 8, 3, 3, 7, 7, 4, 4, 6, 6, 7, 6, 5, 4},
			},
			"baseIncuts": []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 10},
			"freeIncuts": []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 10},
			"statTablo": map[string]interface{}{
				"volatility": 10, "bigwin": 9, "epicwin": 9, "bonus": 7, "show": 1, "rtp": 96.08,
			},
			"maxWinFreq_big":      278047,
			"arrlimits_winLimitK": []int{5000},
			"wildIds":             []int{0},
			"scatterIds":          []int{11, 12},
			"minScatters":         []int{3},
			"incutIds":            []int{9},
			"goldenHogTape_base":  [][]int{{16, 15, 14, 13, 16, 13, 15, 14, 16, 15, 15, 13, 13, 19, 14, 16, 15, 18, 16, 15, 15, 16, 14, 17, 18, 14, 19, 13, 14, 14, 16, 14, 14, 15, 14, 15, 14, 13, 16, 16, 16, 14, 14, 14, 14, 18, 13, 17, 17, 13, 14, 13, 16, 15, 16, 16, 13, 16, 15, 15, 15, 15, 19, 16, 15, 20, 14}},
			"goldenHogTape_free":  [][]int{{16, 16, 25, 15, 15, 16, 15, 13, 14, 13, 16, 26, 17, 13, 19, 24, 13, 15, 14, 14, 15, 18, 15, 14, 15, 18, 14, 15, 17, 13, 14, 14, 14, 24, 14, 14, 26, 14, 16, 15, 16, 16, 15, 15, 19, 18, 14, 16, 14, 16, 15, 14, 14, 19, 13, 13, 13, 16, 13, 16, 16, 14, 16, 15, 15, 15, 16, 14, 20, 13, 25, 16, 17}},
			"extraReelsTape":      [][]int{{9, 9, 9, 9, 28, 23, 9, 28, 9, 28, 23, 9, 28, 23, 9, 24, 25, 9, 9, 9, 28, 24, 9, 23, 9, 9, 28, 9, 9, 28, 9, 9, 28, 9, 25, 9, 26, 9, 9, 28, 9, 23, 9, 9, 24, 9, 9, 23, 28, 9, 9, 9, 23}},
			"reelsWithoutSafe": [][]int{{1, 2, 3, 4, 8, 8, 8, 8, 0, 5, 6, 7, 8, 1, 1, 6, 6, 6, 1, 2, 3, 4, 8, 8, 8, 8, 0, 5, 6, 7, 8, 1, 1, 6, 6, 6, 0, 2, 2, 2, 2, 1, 1, 1, 3, 3, 5, 5, 5, 5, 6, 6, 8, 1, 1, 1, 1, 3, 3, 3, 0, 4, 4, 4, 5, 5, 5, 6, 8, 8, 5, 5, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 7, 7, 6, 6, 6, 6, 7, 7, 7, 0, 5, 8, 8, 8},
				{5, 5, 4, 4, 0, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 5, 5, 4, 4, 0, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 1, 2, 2, 3, 3, 4, 4, 4, 5, 6, 6, 4, 4, 1, 5, 5, 2, 6, 6, 3, 3, 7, 7, 4, 4, 2, 2, 2, 2, 1, 1, 2, 2, 8, 8, 3, 3, 0, 3, 8, 8, 4, 4, 4, 5, 5, 3, 3, 6, 6, 2, 2, 7, 7, 1, 1, 8, 8, 1, 1, 5, 5, 5, 2, 2, 2, 6, 6, 7, 3, 4, 4},
				{1, 1, 5, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 1, 5, 5, 2, 3, 3, 4, 0, 4, 5, 5, 0, 6, 6, 6, 1, 1, 1, 1, 2, 2, 2, 3, 5, 5, 5, 6, 7, 7, 7, 7, 0, 8, 8, 8, 8, 2, 2, 3, 3, 3, 4, 4, 1, 2, 2, 2, 2, 4, 4, 4, 4, 5, 0, 1, 1, 1, 3, 3, 8, 7, 7, 2, 3, 4, 5, 6, 6, 3, 2, 2, 1, 7, 6, 6, 5, 5, 5, 5, 4, 4},
				{5, 5, 4, 4, 0, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 5, 5, 4, 4, 0, 3, 3, 1, 1, 1, 4, 4, 6, 6, 7, 7, 0, 1, 2, 2, 3, 3, 4, 4, 4, 5, 6, 6, 4, 4, 1, 5, 5, 2, 6, 6, 3, 3, 7, 7, 4, 4, 2, 2, 2, 2, 1, 1, 2, 2, 8, 8, 3, 3, 0, 3, 8, 8, 4, 4, 4, 5, 5, 3, 3, 6, 6, 2, 2, 7, 7, 1, 1, 8, 8, 1, 1, 5, 5, 5, 2, 2, 2, 6, 6, 7, 3, 4, 4},
				{1, 1, 2, 2, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 1, 1, 2, 2, 2, 8, 8, 3, 3, 0, 7, 7, 7, 4, 4, 4, 6, 5, 5, 6, 6, 1, 1, 1, 5, 5, 2, 2, 6, 3, 0, 7, 7, 4, 4, 8, 8, 8, 5, 2, 2, 2, 2, 6, 6, 6, 1, 1, 1, 1, 2, 2, 5, 3, 3, 3, 0, 6, 4, 4, 8, 8, 8, 8, 5, 5, 1, 2, 2, 8, 8, 3, 3, 7, 7, 4, 4, 6, 6, 7, 6, 5, 4},
			},
			"freezeMax":       3,
			"volatility":      5,
			"sasAdditionalId": "RIH",
			"sasPaytableId":   "RIH9605",
		},
		"helpInfo": map[string]interface{}{
			"paytable": []interface{}{
				[]interface{}{[]int{0, 1}, []int{5, 1000}, []int{4, 250}, []int{3, 50}},
				[]interface{}{[]int{1, 4}, []int{5, 500}, []int{4, 150}, []int{3, 30}},
				[]interface{}{[]int{2, 4}, []int{5, 400}, []int{4, 100}, []int{3, 20}},
				[]interface{}{[]int{3, 4}, []int{5, 150}, []int{4, 50}, []int{3, 10}},
				[]interface{}{[]int{4, 4}, []int{5, 100}, []int{4, 40}, []int{3, 8}},
				[]interface{}{[]int{5, 4}, []int{5, 50}, []int{4, 10}, []int{3, 2}},
				[]interface{}{[]int{6, 4}, []int{5, 50}, []int{4, 10}, []int{3, 2}},
				[]interface{}{[]int{7, 4}, []int{5, 50}, []int{4, 10}, []int{3, 2}},
				[]interface{}{[]int{8, 4}, []int{5, 50}, []int{4, 10}, []int{3, 2}},
				[]interface{}{[]int{9, 4}},
				[]interface{}{[]int{10, 2}},
				[]interface{}{[]int{11, 8}},
				[]interface{}{[]int{12, 8}},
				[]interface{}{[]int{13, 128}, []int{1, 1}},
				[]interface{}{[]int{14, 128}, []int{1, 2}},
				[]interface{}{[]int{15, 128}, []int{1, 5}},
				[]interface{}{[]int{16, 128}, []int{1, 10}},
				[]interface{}{[]int{17, 128}, []int{1, 25}},
				[]interface{}{[]int{18, 128}, []int{1, 50}},
				[]interface{}{[]int{19, 128}, []int{1, 100}},
				[]interface{}{[]int{20, 128}, []int{1, 500}},
				[]interface{}{[]int{21, 128}, []int{1, 1000}},
				[]interface{}{[]int{22, 128}, []int{1, 2500}},
				[]interface{}{[]int{23, 2}, []int{1, 1}},
				[]interface{}{[]int{24, 2}, []int{1, 2}},
				[]interface{}{[]int{25, 2}, []int{1, 3}},
				[]interface{}{[]int{26, 2}, []int{1, 5}},
				[]interface{}{[]int{27, 2}},
				[]interface{}{[]int{28, 32}},
			},
			"fg": map[string]interface{}{
				"firstAward": 1,
				"limit":      1000000000,
				"portions":   1,
			},
			"doubles": []interface{}{[]interface{}{"off", 0, 0}},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"antiDynamiteBet":        nil,
		"dramshow":               nil,
		"versions": map[string]interface{}{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]interface{}{
			"maxWinLimitK":         5000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 5000,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []int{20},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox": [][]int{
			{1, 7, 6, 2, 6},
			{1, 3, 1, 2, 3},
			{3, 6, 1, 2, 0},
			{3, 6, 1, 6, 7},
		},
		"stopBox": [][]int{
			{1, 7, 6, 2, 6},
			{1, 3, 1, 2, 3},
			{3, 6, 1, 2, 0},
			{3, 6, 1, 6, 7},
		},
		"incutId":         0,
		"safeIncutId":     nil,
		"prevSafeIncutId": nil,
		"prevSafeMask":    nil,
		"safeMask":        nil,
		"dirShift":        1,
		"startFGBox":      nil,
		"arrLock":         nil,
		"arrLockNew":      nil,
		"upN":             0,
		"upNNext":         0,
		"specMult":        1,
		"extrimeUp":       nil,
		"needBreakSeries": false,
		"startInitBox": [][]int{
			{5, 1, 8, 3, 11},
			{5, 5, 2, 4, 5},
			{5, 2, 2, 4, 5},
			{6, 2, 3, 4, 1},
		},
		"seriesPlaying": 0,
		"ibets_change": []map[string]interface{}{
			{
				"bet":           100,
				"gateId":        0,
				"gateInId":      0,
				"prefixId":      3,
				"seriesPlaying": 0,
				"seriesWin":     0,
				"partWins":      []string{},
				"seedInfo":      map[string]interface{}{"seedstart": 0, "uses": 0, "prevuses": 0, "win1": 0, "winK": 0, "info": nil},
				"seedInfoHeir":  map[string]interface{}{"counter": 0, "hasHeir": 0},
				"stopBox": [][]int{
					{1, 7, 6, 2, 6},
					{1, 3, 1, 2, 3},
					{3, 6, 1, 2, 0},
					{3, 6, 1, 6, 7},
				},
				"infoBeforeFg":    nil,
				"startFGSafeMask": nil,
				"nustops":         []interface{}{nil, nil, nil, nil, nil},
				"safeIncutId":     nil,
				"prevSafeIncutId": nil,
				"prevSafeMask":    nil,
				"safeMask":        nil,
				"dirShift":        1,
				"startFGBox":      nil,
				"arrLock":         nil,
				"arrLockNew":      nil,
				"upN":             0,
				"upNNext":         0,
				"specMult":        1,
				"extrimeUp":       nil,
				"needBreakSeries": false,
				"startBox": [][]int{
					{1, 7, 6, 2, 6},
					{1, 3, 1, 2, 3},
					{3, 6, 1, 2, 0},
					{3, 6, 1, 6, 7},
				},
				"seriesStatus": 1,
			},
			{
				"bet":           20,
				"gateId":        0,
				"gateInId":      0,
				"prefixId":      3,
				"seriesPlaying": 0,
				"seriesWin":     0,
				"partWins":      []string{},
				"seedInfo":      map[string]interface{}{"seedstart": 0, "uses": 0, "prevuses": 0, "win1": 0, "winK": 0, "info": nil},
				"seedInfoHeir":  map[string]interface{}{"counter": 0, "hasHeir": 0},
				"stopBox": [][]int{
					{8, 7, 7, 4, 3},
					{1, 7, 0, 0, 3},
					{1, 4, 8, 3, 0},
					{1, 4, 8, 3, 7},
				},
				"infoBeforeFg":    nil,
				"startFGSafeMask": nil,
				"nustops":         []interface{}{nil, nil, nil, nil, nil},
				"safeIncutId":     nil,
				"prevSafeIncutId": nil,
				"prevSafeMask":    nil,
				"safeMask":        nil,
				"dirShift":        1,
				"startFGBox":      nil,
				"arrLock":         nil,
				"arrLockNew":      []bool{false, false, false, false, false},
				"upN":             0,
				"upNNext":         0,
				"specMult":        1,
				"extrimeUp":       nil,
				"needBreakSeries": false,
				"startBox": [][]int{
					{8, 7, 7, 4, 3},
					{1, 7, 0, 0, 3},
					{1, 4, 8, 3, 0},
					{1, 4, 8, 3, 7},
				},
				"seriesStatus": 1,
			},
			{
				"bet":           40,
				"gateId":        0,
				"gateInId":      0,
				"prefixId":      3,
				"seriesPlaying": 0,
				"seriesWin":     0,
				"partWins":      []string{},
				"seedInfo":      map[string]interface{}{"seedstart": 0, "uses": 0, "prevuses": 0, "win1": 0, "winK": 0, "info": nil},
				"seedInfoHeir":  map[string]interface{}{"counter": 0, "hasHeir": 0},
				"stopBox": [][]int{
					{1, 6, 0, 2, 11},
					{1, 4, 6, 2, 7},
					{3, 5, 6, 7, 6},
					{3, 5, 6, 7, 5},
				},
				"infoBeforeFg":    nil,
				"startFGSafeMask": nil,
				"nustops":         []interface{}{nil, nil, nil, nil, nil},
				"safeIncutId":     nil,
				"prevSafeIncutId": nil,
				"prevSafeMask":    nil,
				"safeMask":        nil,
				"dirShift":        1,
				"startFGBox":      nil,
				"arrLock":         nil,
				"arrLockNew":      nil,
				"upN":             0,
				"upNNext":         0,
				"specMult":        1,
				"extrimeUp":       nil,
				"needBreakSeries": false,
				"startBox": [][]int{
					{1, 6, 0, 2, 11},
					{1, 4, 6, 2, 7},
					{3, 5, 6, 7, 6},
					{3, 5, 6, 7, 5},
				},
				"seriesStatus": 1,
			},
		},
		"ibets_BB": []map[string]interface{}{
			{
				"bet":          20,
				"gateId":       0,
				"gateInId":     0,
				"prefixId":     3,
				"seriesWin":    0,
				"partWins":     []string{},
				"seedInfo":     map[string]interface{}{"seedstart": 0, "uses": 0, "prevuses": 0, "win1": 0, "winK": 0, "info": nil},
				"seedInfoHeir": map[string]interface{}{"counter": 0, "hasHeir": 0},
				"stopBox": [][]int{
					{3, 1, 1, 4, 7},
					{3, 1, 1, 4, 4},
					{3, 2, 1, 5, 4},
					{0, 2, 5, 6, 4},
				},
				"infoBeforeFg":    nil,
				"startFGSafeMask": nil,
				"nustops":         []interface{}{nil, nil, nil, nil, nil},
				"safeIncutId":     nil,
				"prevSafeMask":    nil,
				"safeMask":        nil,
				"dirShift":        1,
				"startFGBox":      nil,
				"arrLock":         nil,
				"arrLockNew":      nil,
				"upN":             0,
				"upNNext":         0,
				"specMult":        1,
				"extrimeUp":       nil,
				"startBox": [][]int{
					{3, 1, 1, 4, 7},
					{3, 1, 1, 4, 4},
					{3, 2, 1, 5, 4},
					{0, 2, 5, 6, 4},
				},
				"seriesStatus": 0,
			},
		},
		"helpseed": true,
	}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (g400128) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	default:
		return 100
	}
}

func (m g400128) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
